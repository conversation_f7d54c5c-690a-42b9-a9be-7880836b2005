from pyrogram import C<PERSON>
from pyrogram.types import <PERSON>t<PERSON><PERSON>ber
from pyrogram.errors import <PERSON><PERSON><PERSON>, <PERSON>t<PERSON><PERSON><PERSON><PERSON>equired, PeerIdInvalid, ChannelPrivate
from typing import List, Dict, Any
import asyncio
import logging

logger = logging.getLogger(__name__)


class UserExtractor:
    """Handles extraction of users from Telegram groups"""

    def __init__(self, client: Client):
        self.client = client

    async def extract_users_from_group(self, group_identifier: str, progress_callback=None) -> tuple[List[Dict[str, Any]], str]:
        """
        Extract all available participants from a Telegram group

        Args:
            group_identifier: Group username (@groupname) or ID (-100123456789)
            progress_callback: Optional callback function for progress updates

        Returns:
            Tuple of (users_data, group_name)
        """
        try:
            # Check if it's an invite link (bot cannot handle these)
            if (group_identifier.startswith('https://t.me/+') or
                group_identifier.startswith('https://t.me/joinchat/') or
                group_identifier.startswith('+')):

                raise ValueError(
                    "❌ **Bot Method Cannot Handle Private Invite Links**\n\n"
                    "The standard bot extraction method cannot access private invite links.\n\n"
                    "**Please use the message extraction method instead:**\n"
                    f"• `/extract_messages {group_identifier}`\n\n"
                    "**Why this happens:**\n"
                    "• Bots cannot join groups via invite links\n"
                    "• The message method uses a user session which can join private groups\n"
                    "• User sessions have better access to group members\n\n"
                    "**Alternative:**\n"
                    "• Add the bot to the group manually\n"
                    "• Then use `/extract` with the group username or ID"
                )

            # Get chat information
            chat = await self.client.get_chat(group_identifier)
            group_name = chat.title or str(group_identifier)

            if progress_callback:
                await progress_callback(f"📊 Found group: {group_name}")
                await progress_callback(f"👥 Total members: {chat.members_count}")
                await progress_callback(f"🔍 Group type: {chat.type.name}")

            # Check if we can access members
            if chat.type.name not in ['GROUP', 'SUPERGROUP', 'CHANNEL']:
                raise ValueError("This is not a group or channel")

            users_data = []
            all_user_ids = set()  # To avoid duplicates

            if progress_callback:
                await progress_callback("🔄 Getting available participants...")

            # Simple approach: Just get all available participants
            try:
                count = 0
                async for member in self.client.get_chat_members(group_identifier):
                    try:
                        # Skip if we already have this user
                        if member.user.id in all_user_ids:
                            continue

                        user_data = self._extract_user_data(member)
                        users_data.append(user_data)
                        all_user_ids.add(member.user.id)
                        count += 1

                        # Progress update every 25 users
                        if progress_callback and count % 25 == 0:
                            await progress_callback(f"📥 Extracted {count} participants...")

                    except Exception as e:
                        logger.warning(f"Error extracting user data: {e}")
                        continue

                if progress_callback:
                    await progress_callback(f"✅ Found {count} available participants")

            except FloodWait as e:
                if progress_callback:
                    await progress_callback(f"⏳ Rate limited. Waiting {e.value} seconds...")
                await asyncio.sleep(e.value)
                # Retry once after flood wait
                try:
                    async for member in self.client.get_chat_members(group_identifier):
                        if member.user.id not in all_user_ids:
                            user_data = self._extract_user_data(member)
                            users_data.append(user_data)
                            all_user_ids.add(member.user.id)
                except Exception as retry_error:
                    logger.warning(f"Retry failed: {retry_error}")

            if progress_callback:
                await progress_callback(f"✅ Extraction complete! Total participants: {len(users_data)}")

            return users_data, group_name
            
        except FloodWait as e:
            error_msg = f"Rate limited. Please wait {e.value} seconds and try again."
            logger.error(error_msg)
            raise Exception(error_msg)
            
        except ChatAdminRequired:
            error_msg = "Bot needs admin rights to access member list."
            logger.error(error_msg)
            raise Exception(error_msg)
            
        except PeerIdInvalid:
            error_msg = "Invalid group ID or username. Make sure the bot is added to the group."
            logger.error(error_msg)
            raise Exception(error_msg)
            
        except ChannelPrivate:
            error_msg = "Cannot access private channel/group. Make sure the bot is added as member."
            logger.error(error_msg)
            raise Exception(error_msg)
            
        except Exception as e:
            error_msg = f"Error extracting users: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def _extract_user_data(self, member: ChatMember) -> Dict[str, Any]:
        """
        Extract relevant data from a ChatMember object
        
        Args:
            member: ChatMember object from Pyrogram
            
        Returns:
            Dictionary with user data
        """
        user = member.user
        
        # Basic user information
        user_data = {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
        }
        
        # Member status and permissions
        user_data.update({
            'status': member.status.name if member.status else 'UNKNOWN',
            'joined_date': member.joined_date.isoformat() if member.joined_date else None,
            'is_member': member.status.name in ['MEMBER', 'ADMINISTRATOR', 'OWNER'] if member.status else False,
        })
        
        # Last online information
        if hasattr(user, 'last_online_date') and user.last_online_date:
            user_data['last_online_date'] = user.last_online_date.isoformat()
        else:
            user_data['last_online_date'] = None
        
        # Admin-specific information
        if member.status and member.status.name in ['ADMINISTRATOR', 'OWNER']:
            user_data.update({
                'is_admin': True,
                'admin_title': getattr(member, 'custom_title', None),
                'can_be_edited': getattr(member, 'can_be_edited', False),
                'can_manage_chat': getattr(member, 'can_manage_chat', False),
                'can_delete_messages': getattr(member, 'can_delete_messages', False),
                'can_manage_video_chats': getattr(member, 'can_manage_video_chats', False),
                'can_restrict_members': getattr(member, 'can_restrict_members', False),
                'can_promote_members': getattr(member, 'can_promote_members', False),
                'can_change_info': getattr(member, 'can_change_info', False),
                'can_invite_users': getattr(member, 'can_invite_users', False),
                'can_pin_messages': getattr(member, 'can_pin_messages', False),
            })
        else:
            user_data['is_admin'] = False
        
        return user_data

from pyrogram import Client
from pyrogram.types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pyrogram.errors import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>quired, PeerIdInvalid, ChannelPrivate
from pyrogram import enums
from typing import List, Dict, Any, Optional
import asyncio
import logging
from datetime import datetime
from config import MAX_USERS_PER_REQUEST

logger = logging.getLogger(__name__)


class UserExtractor:
    """Handles extraction of users from Telegram groups"""

    def __init__(self, client: Client):
        self.client = client

    async def extract_users_from_group(self, group_identifier: str, progress_callback=None) -> tuple[List[Dict[str, Any]], str]:
        """
        Extract all users from a Telegram group

        Args:
            group_identifier: Group username (@groupname) or ID (-100123456789)
            progress_callback: Optional callback function for progress updates

        Returns:
            Tuple of (users_data, group_name)
        """
        try:
            # First, try to join the group if it's public
            await self._try_join_group(group_identifier, progress_callback)

            # Get chat information
            chat = await self.client.get_chat(group_identifier)
            group_name = chat.title or str(group_identifier)

            if progress_callback:
                await progress_callback(f"📊 Found group: {group_name}")
                await progress_callback(f"👥 Total members: {chat.members_count}")
                await progress_callback(f"🔍 Group type: {chat.type.name}")

            # Check if we can access members
            if chat.type.name not in ['GROUP', 'SUPERGROUP', 'CHANNEL']:
                raise ValueError("This is not a group or channel")

            users_data = []
            processed_count = 0

            if progress_callback:
                await progress_callback("🔄 Starting user extraction...")

            # Try different extraction methods based on group type and size
            all_user_ids = set()  # To avoid duplicates

            # Method 1: Try to get all members directly
            await self._extract_with_member_filters(group_identifier, users_data, all_user_ids, progress_callback)

            # Method 2: Try to get more users with different approaches
            if progress_callback:
                await progress_callback("🔍 Trying additional extraction methods...")

            # Only try message history if we have very few users and it's a small group
            if len(users_data) < 20:
                try:
                    if progress_callback:
                        await progress_callback("📜 Attempting to get recent messages (limited for bots)...")
                    await self._extract_users_from_recent_activity(group_identifier, users_data, all_user_ids, progress_callback)
                except Exception as e:
                    if progress_callback:
                        await progress_callback("ℹ️ Message history not available for bots in this group")

            # Method 3: If still limited, try alphabet search for usernames
            if len(users_data) < 50 and chat.type.name in ['SUPERGROUP', 'CHANNEL']:
                if progress_callback:
                    await progress_callback("🔤 Trying alphabet search for more users...")
                await self._extract_with_alphabet_search(group_identifier, users_data, all_user_ids, progress_callback)

            if progress_callback:
                await progress_callback(f"✅ Extraction complete! Total users: {len(users_data)}")

            return users_data, group_name
            
        except FloodWait as e:
            error_msg = f"Rate limited. Please wait {e.value} seconds and try again."
            logger.error(error_msg)
            raise Exception(error_msg)
            
        except ChatAdminRequired:
            error_msg = "Bot needs admin rights to access member list."
            logger.error(error_msg)
            raise Exception(error_msg)
            
        except PeerIdInvalid:
            error_msg = "Invalid group ID or username. Make sure the bot is added to the group."
            logger.error(error_msg)
            raise Exception(error_msg)
            
        except ChannelPrivate:
            error_msg = "Cannot access private channel/group. Make sure the bot is added as member."
            logger.error(error_msg)
            raise Exception(error_msg)
            
        except Exception as e:
            error_msg = f"Error extracting users: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def _extract_user_data(self, member: ChatMember) -> Dict[str, Any]:
        """
        Extract relevant data from a ChatMember object
        
        Args:
            member: ChatMember object from Pyrogram
            
        Returns:
            Dictionary with user data
        """
        user = member.user
        
        # Basic user information
        user_data = {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
        }
        
        # Member status and permissions
        user_data.update({
            'status': member.status.name if member.status else 'UNKNOWN',
            'joined_date': member.joined_date.isoformat() if member.joined_date else None,
            'is_member': member.status.name in ['MEMBER', 'ADMINISTRATOR', 'OWNER'] if member.status else False,
        })
        
        # Last online information
        if hasattr(user, 'last_online_date') and user.last_online_date:
            user_data['last_online_date'] = user.last_online_date.isoformat()
        else:
            user_data['last_online_date'] = None
        
        # Admin-specific information
        if member.status and member.status.name in ['ADMINISTRATOR', 'OWNER']:
            user_data.update({
                'is_admin': True,
                'admin_title': getattr(member, 'custom_title', None),
                'can_be_edited': getattr(member, 'can_be_edited', False),
                'can_manage_chat': getattr(member, 'can_manage_chat', False),
                'can_delete_messages': getattr(member, 'can_delete_messages', False),
                'can_manage_video_chats': getattr(member, 'can_manage_video_chats', False),
                'can_restrict_members': getattr(member, 'can_restrict_members', False),
                'can_promote_members': getattr(member, 'can_promote_members', False),
                'can_change_info': getattr(member, 'can_change_info', False),
                'can_invite_users': getattr(member, 'can_invite_users', False),
                'can_pin_messages': getattr(member, 'can_pin_messages', False),
            })
        else:
            user_data['is_admin'] = False
        
        return user_data

    async def _try_join_group(self, group_identifier: str, progress_callback=None):
        """
        Try to join the group if it's public and we're not already a member
        """
        try:
            if progress_callback:
                await progress_callback("🚪 Checking if bot can join the group...")

            # Try to join the group
            await self.client.join_chat(group_identifier)

            if progress_callback:
                await progress_callback("✅ Successfully joined the group!")

        except Exception as e:
            # It's okay if we can't join (already member, private group, etc.)
            logger.info(f"Could not join group (this is normal): {e}")
            if progress_callback:
                await progress_callback("ℹ️ Using existing access to the group")

    async def _extract_with_member_filters(self, group_identifier: str, users_data: List[Dict[str, Any]],
                                          all_user_ids: set, progress_callback=None):
        """
        Extract users using different member filters
        """
        filters_to_try = [
            None,  # Default - gets all visible members
            enums.ChatMembersFilter.SEARCH,  # Search filter
            enums.ChatMembersFilter.RECENT,  # Recent members
            enums.ChatMembersFilter.ADMINISTRATORS,  # Admins
            enums.ChatMembersFilter.BOTS,  # Bots
        ]

        for filter_type in filters_to_try:
            try:
                if progress_callback:
                    filter_name = filter_type.name if filter_type else "ALL"
                    await progress_callback(f"🔍 Extracting with filter: {filter_name}")

                # Extract with current filter
                if filter_type:
                    members_generator = self.client.get_chat_members(group_identifier, filter=filter_type)
                else:
                    members_generator = self.client.get_chat_members(group_identifier)

                count = 0
                async for member in members_generator:
                    try:
                        # Skip if we already have this user
                        if member.user.id in all_user_ids:
                            continue

                        user_data = self._extract_user_data(member)
                        users_data.append(user_data)
                        all_user_ids.add(member.user.id)
                        count += 1

                        # Progress update every 25 users for this filter
                        if progress_callback and count % 25 == 0:
                            await progress_callback(f"📥 Found {count} users with {filter_name} filter...")

                    except Exception as e:
                        logger.warning(f"Error extracting user data: {e}")
                        continue

                if progress_callback and count > 0:
                    await progress_callback(f"✅ {filter_name} filter found {count} users")

            except FloodWait as e:
                if progress_callback:
                    await progress_callback(f"⏳ Rate limited. Waiting {e.value} seconds...")
                await asyncio.sleep(e.value)
                continue

            except Exception as e:
                logger.warning(f"Error with filter {filter_type}: {e}")
                continue

    async def _extract_with_alphabet_search(self, group_identifier: str, users_data: List[Dict[str, Any]],
                                           all_user_ids: set, progress_callback=None):
        """
        Try to search for users using alphabet characters
        """
        try:
            search_chars = "abcdefghijklmnopqrstuvwxyz0123456789"

            for char in search_chars:
                try:
                    if progress_callback:
                        await progress_callback(f"🔤 Searching for users starting with '{char}'...")

                    count = 0
                    async for member in self.client.get_chat_members(
                        group_identifier,
                        query=char,
                        filter=enums.ChatMembersFilter.SEARCH
                    ):
                        try:
                            if member.user.id in all_user_ids:
                                continue

                            user_data = self._extract_user_data(member)
                            users_data.append(user_data)
                            all_user_ids.add(member.user.id)
                            count += 1

                        except Exception as e:
                            logger.warning(f"Error extracting user data: {e}")
                            continue

                    if count > 0 and progress_callback:
                        await progress_callback(f"🔤 Found {count} users with '{char}'")

                    # Small delay between searches
                    await asyncio.sleep(0.5)

                except FloodWait as e:
                    if progress_callback:
                        await progress_callback(f"⏳ Rate limited. Waiting {e.value} seconds...")
                    await asyncio.sleep(e.value)
                    continue

                except Exception as e:
                    logger.warning(f"Error searching with '{char}': {e}")
                    continue

        except Exception as e:
            logger.error(f"Error in alphabet search: {e}")

    async def _extract_users_from_recent_activity(self, group_identifier: str, users_data: List[Dict[str, Any]],
                                                 all_user_ids: set, progress_callback=None):
        """
        Try to extract users using bot-compatible methods only

        Args:
            group_identifier: Group username or ID
            users_data: Existing users data list to append to
            all_user_ids: Set of already processed user IDs
            progress_callback: Optional callback for progress updates
        """
        try:
            new_users_found = 0

            # Method 1: Try to get chat members with different limits
            for limit in [200, 100, 50, 25]:
                try:
                    if progress_callback:
                        await progress_callback(f"🔍 Trying to get {limit} members...")

                    count = 0
                    async for member in self.client.get_chat_members(group_identifier, limit=limit):
                        if member.user.id not in all_user_ids:
                            user_data = self._extract_user_data(member)
                            users_data.append(user_data)
                            all_user_ids.add(member.user.id)
                            new_users_found += 1
                            count += 1

                    if count > 0:
                        if progress_callback:
                            await progress_callback(f"✅ Found {count} users with limit {limit}")
                        break  # If we found users, no need to try smaller limits

                except Exception as e:
                    logger.warning(f"Error with limit {limit}: {e}")
                    continue

            # Method 2: Try common username patterns (bot-compatible search)
            if new_users_found < 10:
                common_patterns = ['a', 'b', 'c', 'd', 'e', 'admin', 'user', 'test', '1', '2']

                for pattern in common_patterns:
                    try:
                        if progress_callback:
                            await progress_callback(f"🔤 Searching for pattern '{pattern}'...")

                        count = 0
                        async for member in self.client.get_chat_members(
                            group_identifier,
                            query=pattern,
                            filter=enums.ChatMembersFilter.SEARCH,
                            limit=50
                        ):
                            if member.user.id not in all_user_ids:
                                user_data = self._extract_user_data(member)
                                users_data.append(user_data)
                                all_user_ids.add(member.user.id)
                                new_users_found += 1
                                count += 1

                        if count > 0 and progress_callback:
                            await progress_callback(f"🔤 Pattern '{pattern}' found {count} users")

                        # Small delay between searches
                        await asyncio.sleep(0.3)

                    except Exception as e:
                        logger.warning(f"Error searching pattern '{pattern}': {e}")
                        continue

            if progress_callback and new_users_found > 0:
                await progress_callback(f"✅ Found {new_users_found} additional users using bot methods")

        except Exception as e:
            logger.error(f"Error in recent activity extraction: {e}")
            # Don't raise - this is a fallback method

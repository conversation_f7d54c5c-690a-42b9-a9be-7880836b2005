"""
Message-based user extractor that acts like a normal user
Joins group and extracts users from messages without using get_chat_history
"""

from pyrogram import Client
from pyrogram.types import Message
from pyrogram.errors import FloodWait, Chat<PERSON>d<PERSON>Required, PeerIdInvalid, ChannelPrivate
from typing import List, Dict, Any, Set
import asyncio
import logging
import os
from config import API_ID, API_HASH

logger = logging.getLogger(__name__)


class MessageUserExtractor:
    """
    Extracts users by acting like a normal user and getting messages
    """
    
    def __init__(self, bot_client: Client):
        self.bot_client = bot_client
        self.user_client = None
        self.is_user_client_active = False

    
    async def create_user_session(self, session_name: str = "message_extractor_user"):
        """
        Create a user session that acts like a normal user
        """
        try:
            # Stop any existing user client
            if self.user_client and self.is_user_client_active:
                await self.user_client.stop()

            # Create new user client (NOT bot client)
            self.user_client = Client(
                session_name,
                api_id=API_ID,
                api_hash=API_HASH,
                # No bot_token - this makes it a user client
            )

            # Start user client (will prompt for phone if needed)
            await self.user_client.start()
            me = await self.user_client.get_me()
            self.is_user_client_active = True
            logger.info(f"User client started: {me.first_name} (@{me.username if me.username else 'no_username'})")
            return True

        except Exception as e:
            logger.error(f"Failed to create user session: {e}")
            self.is_user_client_active = False
            if self.user_client:
                try:
                    await self.user_client.stop()
                except:
                    pass
                self.user_client = None
            return False
    
    async def extract_users_from_messages(self, group_identifier: str, progress_callback=None) -> tuple[List[Dict[str, Any]], str, List[Dict[str, Any]]]:
        """
        Extract users and messages by joining group and getting messages like a normal user

        Args:
            group_identifier: Group username (@groupname) or ID (-100123456789)
            progress_callback: Optional callback function for progress updates

        Returns:
            Tuple of (users_data, group_name, messages_data)
        """
        if not self.is_user_client_active or not self.user_client:
            raise ValueError("User client not active. Call create_user_session first and ensure it succeeded.")

        try:
            # Verify user client is working
            try:
                me = await self.user_client.get_me()
                if progress_callback:
                    await progress_callback(f"✅ User session active: {me.first_name}")
            except Exception as e:
                raise ValueError(f"User client not working properly: {e}")

            # Step 1: Resolve the group identifier and join if needed
            resolved_identifier = await self._resolve_and_join_group(group_identifier, progress_callback)

            # Step 2: Get group info using USER client with resolved identifier
            chat = await self.user_client.get_chat(resolved_identifier)
            group_name = chat.title or str(group_identifier)

            if progress_callback:
                await progress_callback(f"📊 Accessing group: {group_name}")
                await progress_callback(f"👥 Total members: {chat.members_count}")
                await progress_callback(f"🔍 Acting as normal user to extract from messages")

            # Step 3: Extract users and messages from chunks
            users_data = []
            messages_data = []
            all_user_ids: Set[int] = set()

            if progress_callback:
                await progress_callback("📨 Starting to collect messages and extract users...")

            # Get messages in chunks to avoid limitations
            await self._extract_users_and_messages_from_chunks(
                resolved_identifier, users_data, messages_data, all_user_ids, chat, group_name, progress_callback
            )

            if progress_callback:
                await progress_callback(
                    f"✅ Extraction complete! Found {len(users_data)} unique users "
                    f"and {len(messages_data)} messages"
                )

            return users_data, group_name, messages_data

        except Exception as e:
            logger.error(f"Error in message-based extraction: {e}")
            raise

    async def extract_users_only(self, group_identifier: str, progress_callback=None):
        """
        Extract only users (no messages) using user session - for /extract command

        Args:
            group_identifier: Group username, ID, or invite link
            progress_callback: Optional callback for progress updates

        Returns:
            Tuple of (users_data, group_name)
        """
        try:
            if not self.user_client or not self.is_user_client_active:
                raise ValueError("User client not active. Call create_user_session() first.")

            if progress_callback:
                await progress_callback("🔍 Verifying user session...")

            # Verify user client is working
            try:
                me = await self.user_client.get_me()
                if progress_callback:
                    await progress_callback(f"✅ User session active: {me.first_name}")
            except Exception as e:
                raise ValueError(f"User client not working properly: {e}")

            # Step 1: Resolve the group identifier and join if needed
            resolved_identifier = await self._resolve_and_join_group(group_identifier, progress_callback)

            # Step 2: Get group info using USER client with resolved identifier
            chat = await self.user_client.get_chat(resolved_identifier)
            group_name = chat.title or str(group_identifier)

            if progress_callback:
                await progress_callback(f"📊 Accessing group: {group_name}")
                await progress_callback(f"👥 Total members: {chat.members_count}")
                await progress_callback("🔍 Acting as normal user to extract users from messages")

            users_data = []
            all_user_ids = set()

            if progress_callback:
                await progress_callback("📨 Starting to collect messages and extract users...")

            # Get users from messages (but don't save messages)
            await self._extract_users_from_chunks_only(
                resolved_identifier, users_data, all_user_ids, chat, group_name, progress_callback
            )

            if progress_callback:
                await progress_callback(
                    f"✅ User extraction complete! Found {len(users_data)} unique users"
                )

            return users_data, group_name

        except Exception as e:
            logger.error(f"Error in user-only extraction: {e}")
            raise

    async def _resolve_and_join_group(self, group_identifier: str, progress_callback=None) -> str:
        """
        Resolve group identifier and join if it's an invite link

        Args:
            group_identifier: Group username, ID, or invite link
            progress_callback: Optional callback for progress updates

        Returns:
            str: Resolved identifier (chat ID or username) for API calls
        """
        try:
            # Check if it's an invite link
            if (group_identifier.startswith('https://t.me/+') or
                group_identifier.startswith('https://t.me/joinchat/') or
                group_identifier.startswith('+')):

                if progress_callback:
                    await progress_callback("🔗 Detected private invite link, attempting to join...")

                # Join the group using the invite link
                chat = await self.user_client.join_chat(group_identifier)

                if progress_callback:
                    await progress_callback(f"✅ Successfully joined group via invite link!")

                # Return the chat ID for future API calls
                return chat.id

            else:
                # It's a regular username or ID
                if progress_callback:
                    await progress_callback("🔍 Using provided identifier...")

                # Try to join if it's a public group (optional)
                try:
                    await self.user_client.join_chat(group_identifier)
                    if progress_callback:
                        await progress_callback("✅ Joined public group")
                except Exception:
                    # It's okay if we can't join (already member, private, etc.)
                    if progress_callback:
                        await progress_callback("ℹ️ Using existing access to group")

                return group_identifier

        except Exception as e:
            logger.error(f"Error resolving group identifier {group_identifier}: {e}")
            # If all else fails, try to use the original identifier
            return group_identifier

    async def _join_group_as_user(self, group_identifier: str, progress_callback=None):
        """
        Join the group as a normal user
        """
        if not self.user_client or not self.is_user_client_active:
            raise ValueError("User client not available for joining group")

        try:
            if progress_callback:
                await progress_callback("🚪 Attempting to join group as user...")

            # Try to join the group using USER client (not bot client)
            await self.user_client.join_chat(group_identifier)

            if progress_callback:
                await progress_callback("✅ Successfully joined the group as user!")

            # Small delay after joining
            await asyncio.sleep(2)

        except Exception as e:
            # It's okay if we can't join (already member, private group, etc.)
            logger.info(f"Could not join group (might already be member): {e}")
            if progress_callback:
                await progress_callback("ℹ️ Using existing access to the group")

            # Check if the error is still about bot methods - this means user client isn't working
            if "BOT_METHOD_INVALID" in str(e):
                raise ValueError(
                    "User client is not working properly - still getting bot method errors. "
                    "Please ensure user session was created successfully."
                )
    
    async def _extract_users_and_messages_from_chunks(self, group_identifier: str, users_data: List[Dict[str, Any]],
                                                     messages_data: List[Dict[str, Any]], all_user_ids: Set[int],
                                                     chat, group_name: str, progress_callback=None):
        """
        Extract users and messages by getting them in chunks
        """
        try:
            chunk_size = 200  # Increased chunk size for better efficiency
            total_messages_processed = 0
            total_users_found = 0
            offset_id = 0
            max_messages = 100000  # Increased to 100k messages
            consecutive_empty_chunks = 0
            last_message_id = None

            if progress_callback:
                await progress_callback("📨 Starting COMPREHENSIVE message extraction (up to 100k messages)...")

            # First, try to get the total message count
            try:
                # Get one message to check if we have access
                test_messages = []
                async for msg in self.user_client.get_chat_history(group_identifier, limit=1):
                    test_messages.append(msg)

                if test_messages and progress_callback:
                    await progress_callback(f"✅ Message access confirmed. Starting full extraction...")
            except Exception as e:
                logger.warning(f"Could not test message access: {e}")
                # Continue anyway - the error might be resolved during actual extraction

            while total_messages_processed < max_messages:
                try:
                    if progress_callback:
                        await progress_callback(f"📨 Getting message chunk (processed: {total_messages_processed})...")
                    
                    # Get messages chunk using user client with better pagination
                    messages = []
                    try:
                        # Use different approaches to get ALL messages
                        if offset_id == 0:
                            # First chunk - get most recent messages
                            async for message in self.user_client.get_chat_history(
                                group_identifier,
                                limit=chunk_size
                            ):
                                messages.append(message)
                        else:
                            # Subsequent chunks - use offset to get older messages
                            # Try multiple methods to ensure we get all messages
                            async for message in self.user_client.get_chat_history(
                                group_identifier,
                                limit=chunk_size,
                                offset_id=offset_id
                            ):
                                messages.append(message)

                        # If we got fewer messages than expected, try alternative method
                        if len(messages) < chunk_size and offset_id > 0:
                            # Try getting messages with offset_date instead
                            if messages:
                                last_date = messages[-1].date
                                additional_messages = []
                                async for message in self.user_client.get_chat_history(
                                    group_identifier,
                                    limit=chunk_size,
                                    offset_date=last_date
                                ):
                                    # Avoid duplicates
                                    if message.id not in [m.id for m in messages]:
                                        additional_messages.append(message)

                                messages.extend(additional_messages[:chunk_size//2])  # Add up to half chunk size

                    except Exception as e:
                        logger.warning(f"Error getting messages chunk: {e}")
                        consecutive_empty_chunks += 1
                        if consecutive_empty_chunks >= 3:
                            if progress_callback:
                                await progress_callback("❌ Multiple failed attempts, stopping extraction")
                            break
                        continue

                    if not messages:
                        consecutive_empty_chunks += 1
                        if progress_callback:
                            await progress_callback(f"📭 Empty chunk #{consecutive_empty_chunks}")

                        # If we get 3 consecutive empty chunks, we've reached the end
                        if consecutive_empty_chunks >= 3:
                            if progress_callback:
                                await progress_callback("📭 Reached end of message history")
                            break
                        continue

                    # Reset empty chunk counter since we got messages
                    consecutive_empty_chunks = 0
                    
                    # Process messages in this chunk
                    chunk_users_found = 0
                    chunk_messages_saved = 0

                    for message in messages:
                        try:
                            # Always save message data
                            message_data = self._extract_message_data(message, chat.id, group_name)
                            messages_data.append(message_data)
                            chunk_messages_saved += 1

                            # Extract user from message sender
                            if message.from_user and message.from_user.id not in all_user_ids:
                                user_data = await self._extract_user_data_from_message(message)
                                users_data.append(user_data)
                                all_user_ids.add(message.from_user.id)
                                chunk_users_found += 1
                                total_users_found += 1

                            # Extract user from forwarded message
                            if (hasattr(message, 'forward_from') and message.forward_from and
                                message.forward_from.id not in all_user_ids):
                                user_data = await self._extract_user_data_from_forward(message.forward_from)
                                users_data.append(user_data)
                                all_user_ids.add(message.forward_from.id)
                                chunk_users_found += 1
                                total_users_found += 1

                            # Extract users from replies
                            if (message.reply_to_message and message.reply_to_message.from_user and
                                message.reply_to_message.from_user.id not in all_user_ids):
                                user_data = await self._extract_user_data_from_message(message.reply_to_message)
                                users_data.append(user_data)
                                all_user_ids.add(message.reply_to_message.from_user.id)
                                chunk_users_found += 1
                                total_users_found += 1

                        except Exception as e:
                            logger.warning(f"Error processing message: {e}")
                            continue
                    
                    total_messages_processed += len(messages)

                    if progress_callback:
                        await progress_callback(
                            f"📨 Chunk complete: {len(messages)} messages saved, "
                            f"{chunk_users_found} new users (total users: {total_users_found}, total messages: {len(messages_data)})"
                        )

                    # Update offset for next chunk - use the ID of the last message
                    if messages:
                        # Set offset to the last message ID to get older messages
                        offset_id = messages[-1].id

                        # Log progress every 1000 messages
                        if total_messages_processed % 1000 == 0 and progress_callback:
                            await progress_callback(
                                f"🔄 Progress: {total_messages_processed} messages processed, "
                                f"{total_users_found} users found"
                            )

                    # Small delay between chunks to avoid rate limiting
                    await asyncio.sleep(0.5)

                    # If we got fewer messages than chunk_size, we might be near the end
                    # But continue until we get consecutive empty chunks
                    if len(messages) < chunk_size:
                        if progress_callback:
                            await progress_callback(f"📉 Smaller chunk received ({len(messages)} messages), continuing...")
                        # Don't break here - let the empty chunk detection handle the end
                
                except FloodWait as e:
                    if progress_callback:
                        await progress_callback(f"⏳ Rate limited. Waiting {e.value} seconds...")
                    await asyncio.sleep(e.value)
                    continue
                    
                except Exception as e:
                    logger.error(f"Error in chunk processing: {e}")
                    break
            
            if progress_callback:
                await progress_callback(
                    f"✅ **Comprehensive Message Extraction Complete!**\n"
                    f"📊 **Final Statistics:**\n"
                    f"• Total Messages Processed: {total_messages_processed:,}\n"
                    f"• Unique Users Found: {total_users_found:,}\n"
                    f"• Message Chunks: {(total_messages_processed // chunk_size) + 1}\n"
                    f"• Max Messages Limit: {max_messages:,}"
                )
                
        except Exception as e:
            logger.error(f"Error in message chunk extraction: {e}")
            raise

    async def _extract_users_from_chunks_only(self, group_identifier: str, users_data: List[Dict[str, Any]],
                                            all_user_ids: Set[int], chat, group_name: str, progress_callback=None):
        """
        Extract users from message chunks without saving messages (for /extract command)
        """
        try:
            chunk_size = 200  # Increased chunk size for better efficiency
            total_messages_processed = 0
            total_users_found = 0
            offset_id = 0
            max_messages = 50000  # Reduced for user-only extraction
            consecutive_empty_chunks = 0

            if progress_callback:
                await progress_callback("📨 Starting user extraction from messages...")

            while total_messages_processed < max_messages:
                try:
                    if progress_callback:
                        await progress_callback(f"📨 Processing messages (found {total_users_found} users so far)...")

                    # Get messages chunk using user client with better pagination
                    messages = []
                    try:
                        # Use different approaches to get ALL messages
                        if offset_id == 0:
                            # First chunk - get most recent messages
                            async for message in self.user_client.get_chat_history(
                                group_identifier,
                                limit=chunk_size
                            ):
                                messages.append(message)
                        else:
                            # Subsequent chunks - use offset to get older messages
                            async for message in self.user_client.get_chat_history(
                                group_identifier,
                                limit=chunk_size,
                                offset_id=offset_id
                            ):
                                messages.append(message)

                        # If we got fewer messages than expected, try alternative method
                        if len(messages) < chunk_size and offset_id > 0:
                            # Try getting messages with offset_date instead
                            if messages:
                                last_date = messages[-1].date
                                additional_messages = []
                                async for message in self.user_client.get_chat_history(
                                    group_identifier,
                                    limit=chunk_size,
                                    offset_date=last_date
                                ):
                                    # Avoid duplicates
                                    if message.id not in [m.id for m in messages]:
                                        additional_messages.append(message)

                                messages.extend(additional_messages[:chunk_size//2])  # Add up to half chunk size

                    except Exception as e:
                        logger.warning(f"Error getting messages chunk: {e}")
                        consecutive_empty_chunks += 1
                        if consecutive_empty_chunks >= 3:
                            break
                        continue

                    if not messages:
                        consecutive_empty_chunks += 1
                        if consecutive_empty_chunks >= 3:
                            if progress_callback:
                                await progress_callback("📨 No more messages found, finishing extraction...")
                            break
                        continue
                    else:
                        consecutive_empty_chunks = 0

                    chunk_users_found = 0

                    # Process each message to extract users (but don't save messages)
                    for message in messages:
                        try:
                            # Extract user from message sender
                            if message.from_user and message.from_user.id not in all_user_ids:
                                user_data = await self._extract_user_data_from_message(message)
                                users_data.append(user_data)
                                all_user_ids.add(message.from_user.id)
                                chunk_users_found += 1
                                total_users_found += 1

                            # Extract user from forwarded message
                            if (hasattr(message, 'forward_from') and message.forward_from and
                                message.forward_from.id not in all_user_ids):
                                user_data = await self._extract_user_data_from_forward(message.forward_from)
                                users_data.append(user_data)
                                all_user_ids.add(message.forward_from.id)
                                chunk_users_found += 1
                                total_users_found += 1

                            # Extract users from replies
                            if (message.reply_to_message and message.reply_to_message.from_user and
                                message.reply_to_message.from_user.id not in all_user_ids):
                                user_data = await self._extract_user_data_from_message(message.reply_to_message)
                                users_data.append(user_data)
                                all_user_ids.add(message.reply_to_message.from_user.id)
                                chunk_users_found += 1
                                total_users_found += 1

                        except Exception as e:
                            logger.warning(f"Error processing message: {e}")
                            continue

                    total_messages_processed += len(messages)

                    if progress_callback:
                        await progress_callback(
                            f"📨 Chunk complete: {len(messages)} messages processed, "
                            f"{chunk_users_found} new users (total users: {total_users_found})"
                        )

                    # Update offset for next chunk - use the ID of the last message
                    if messages:
                        # Set offset to the last message ID to get older messages
                        offset_id = messages[-1].id
                    else:
                        break

                    # Add delay to avoid rate limiting
                    await asyncio.sleep(0.5)

                except FloodWait as e:
                    if progress_callback:
                        await progress_callback(f"⏳ Rate limited, waiting {e.value} seconds...")
                    await asyncio.sleep(e.value)
                    continue
                except Exception as e:
                    logger.warning(f"Error in chunk processing: {e}")
                    consecutive_empty_chunks += 1
                    if consecutive_empty_chunks >= 3:
                        break
                    continue

            if progress_callback:
                await progress_callback(
                    f"✅ User extraction complete! Processed {total_messages_processed} messages, "
                    f"found {total_users_found} unique users"
                )

        except Exception as e:
            logger.error(f"Error in user-only chunk extraction: {e}")
            raise
    
    async def _extract_user_data_from_message(self, message: Message) -> Dict[str, Any]:
        """
        Extract user data from a message sender including profile image URL
        """
        user = message.from_user

        # Get profile image URL
        profile_image_url = await self._get_user_profile_image_url(user)

        return {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
            'status': 'MEMBER',  # From message, so assume member
            'joined_date': None,
            'is_member': True,
            'last_online_date': None,
            'is_admin': False,
            'source': 'message',  # Mark that this user was found from messages
            'message_date': message.date.isoformat() if message.date else None,
            'profile_image_url': profile_image_url,
            'has_profile_photo': profile_image_url is not None,
        }
    
    async def _extract_user_data_from_forward(self, user) -> Dict[str, Any]:
        """
        Extract user data from a forwarded message sender including profile image URL
        """
        # Get profile image URL
        profile_image_url = await self._get_user_profile_image_url(user)

        return {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
            'status': 'UNKNOWN',  # From forward, status unknown
            'joined_date': None,
            'is_member': False,  # Might not be a member
            'last_online_date': None,
            'is_admin': False,
            'source': 'forwarded_message',  # Mark that this user was found from forwarded messages
            'message_date': None,
            'profile_image_url': profile_image_url,
            'has_profile_photo': profile_image_url is not None,
        }

    def _extract_message_data(self, message: Message, chat_id: int, chat_title: str) -> Dict[str, Any]:
        """
        Extract comprehensive message data
        """
        # Basic message info
        message_data = {
            'message_id': message.id,
            'date': message.date.isoformat() if message.date else None,
            'chat_id': chat_id,
            'chat_title': chat_title,
            'message_text': message.text or message.caption or '',
            'message_type': self._get_message_type(message),
            'media_type': self._get_media_type(message),
            'views': getattr(message, 'views', None),
            'edit_date': message.edit_date.isoformat() if getattr(message, 'edit_date', None) else None,
        }

        # Sender info
        if message.from_user:
            message_data.update({
                'user_id': message.from_user.id,
                'username': message.from_user.username,
                'first_name': message.from_user.first_name,
                'last_name': message.from_user.last_name,
                'is_bot': message.from_user.is_bot,
            })
        else:
            message_data.update({
                'user_id': None,
                'username': None,
                'first_name': None,
                'last_name': None,
                'is_bot': False,
            })

        # Forward info
        if hasattr(message, 'forward_from') and message.forward_from:
            message_data.update({
                'is_forwarded': True,
                'forward_from_user_id': message.forward_from.id,
                'forward_from_username': message.forward_from.username,
                'forward_from_first_name': message.forward_from.first_name,
                'forward_from_last_name': message.forward_from.last_name,
            })
        elif hasattr(message, 'forward_from_chat') and message.forward_from_chat:
            message_data.update({
                'is_forwarded': True,
                'forward_from_user_id': None,
                'forward_from_username': None,
                'forward_from_first_name': message.forward_from_chat.title,
                'forward_from_last_name': None,
            })
        else:
            message_data.update({
                'is_forwarded': False,
                'forward_from_user_id': None,
                'forward_from_username': None,
                'forward_from_first_name': None,
                'forward_from_last_name': None,
            })

        # Reply info
        if message.reply_to_message:
            reply_msg = message.reply_to_message
            message_data.update({
                'reply_to_message_id': reply_msg.id,
                'reply_to_user_id': reply_msg.from_user.id if reply_msg.from_user else None,
                'reply_to_username': reply_msg.from_user.username if reply_msg.from_user else None,
            })
        else:
            message_data.update({
                'reply_to_message_id': None,
                'reply_to_user_id': None,
                'reply_to_username': None,
            })

        return message_data

    def _get_message_type(self, message: Message) -> str:
        """Get the type of message"""
        if message.text:
            return 'text'
        elif message.photo:
            return 'photo'
        elif message.video:
            return 'video'
        elif message.audio:
            return 'audio'
        elif message.voice:
            return 'voice'
        elif message.document:
            return 'document'
        elif message.sticker:
            return 'sticker'
        elif message.animation:
            return 'animation'
        elif message.location:
            return 'location'
        elif message.contact:
            return 'contact'
        elif message.poll:
            return 'poll'
        elif message.venue:
            return 'venue'
        elif message.dice:
            return 'dice'
        elif message.video_note:
            return 'video_note'
        else:
            return 'other'

    def _get_media_type(self, message: Message) -> str:
        """Get the media type if any"""
        if message.photo:
            return 'photo'
        elif message.video:
            return 'video'
        elif message.audio:
            return 'audio'
        elif message.voice:
            return 'voice'
        elif message.document:
            return 'document'
        elif message.sticker:
            return 'sticker'
        elif message.animation:
            return 'gif'
        elif message.video_note:
            return 'video_note'
        else:
            return None

    async def _get_user_profile_image_url(self, user) -> str:
        """
        Get user profile image URL/link
        """
        try:
            if not user or not hasattr(user, 'photo') or not user.photo:
                return None

            if not self.user_client or not self.is_user_client_active:
                return None

            # Get the profile photo file info
            if hasattr(user.photo, 'big_file_id') and user.photo.big_file_id:
                # For larger profile photos
                file_id = user.photo.big_file_id
            elif hasattr(user.photo, 'small_file_id') and user.photo.small_file_id:
                # For smaller profile photos
                file_id = user.photo.small_file_id
            else:
                return None

            # Create a Telegram file URL (this is a conceptual URL, actual implementation may vary)
            # In practice, you might want to use the file_id to get the actual download URL
            profile_url = f"tg://file_id/{file_id}"

            # Alternative: Try to get the actual download URL
            try:
                # Get file info which might contain a download URL
                file_info = await self.user_client.get_file(file_id)
                if hasattr(file_info, 'file_path'):
                    # Construct Telegram API URL
                    profile_url = f"https://api.telegram.org/file/bot{self.user_client.bot_token}/{file_info.file_path}"
                else:
                    profile_url = f"tg://file_id/{file_id}"
            except:
                # Fallback to file_id reference
                profile_url = f"tg://file_id/{file_id}"

            return profile_url

        except Exception as e:
            logger.warning(f"Could not get profile image URL for user {user.id if user else 'unknown'}: {e}")
            return None

    async def stop_user_client(self):
        """
        Stop the user client
        """
        if self.user_client and self.is_user_client_active:
            await self.user_client.stop()
            self.is_user_client_active = False
            logger.info("User client stopped")

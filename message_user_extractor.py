"""
Message-based user extractor that acts like a normal user
Joins group and extracts users from messages without using get_chat_history
"""

from pyrogram import Client
from pyrogram.types import Message
from pyrogram.errors import FloodWait, ChatAdminRequired, PeerIdInvalid, ChannelPrivate
from typing import List, Dict, Any, Set
import asyncio
import logging
import os
from config import API_ID, API_HASH

logger = logging.getLogger(__name__)


class MessageUserExtractor:
    """
    Extracts users by acting like a normal user and getting messages
    """
    
    def __init__(self, bot_client: Client):
        self.bot_client = bot_client
        self.user_client = None
        self.is_user_client_active = False
        self.profile_images_dir = "profile_images"
        self._ensure_profile_images_dir()

    def _ensure_profile_images_dir(self):
        """Create profile images directory if it doesn't exist"""
        if not os.path.exists(self.profile_images_dir):
            os.makedirs(self.profile_images_dir)
    
    async def create_user_session(self, session_name: str = "message_extractor_user"):
        """
        Create a user session that acts like a normal user
        """
        try:
            # Stop any existing user client
            if self.user_client and self.is_user_client_active:
                await self.user_client.stop()

            # Create new user client (NOT bot client)
            self.user_client = Client(
                session_name,
                api_id=API_ID,
                api_hash=API_HASH,
                # No bot_token - this makes it a user client
            )

            # Start user client (will prompt for phone if needed)
            await self.user_client.start()
            me = await self.user_client.get_me()
            self.is_user_client_active = True
            logger.info(f"User client started: {me.first_name} (@{me.username if me.username else 'no_username'})")
            return True

        except Exception as e:
            logger.error(f"Failed to create user session: {e}")
            self.is_user_client_active = False
            if self.user_client:
                try:
                    await self.user_client.stop()
                except:
                    pass
                self.user_client = None
            return False
    
    async def extract_users_from_messages(self, group_identifier: str, progress_callback=None) -> tuple[List[Dict[str, Any]], str, List[Dict[str, Any]]]:
        """
        Extract users and messages by joining group and getting messages like a normal user

        Args:
            group_identifier: Group username (@groupname) or ID (-100123456789)
            progress_callback: Optional callback function for progress updates

        Returns:
            Tuple of (users_data, group_name, messages_data)
        """
        if not self.is_user_client_active or not self.user_client:
            raise ValueError("User client not active. Call create_user_session first and ensure it succeeded.")

        try:
            # Verify user client is working
            try:
                me = await self.user_client.get_me()
                if progress_callback:
                    await progress_callback(f"✅ User session active: {me.first_name}")
            except Exception as e:
                raise ValueError(f"User client not working properly: {e}")

            # Step 1: Try to join the group as a user
            await self._join_group_as_user(group_identifier, progress_callback)

            # Step 2: Get group info using USER client
            chat = await self.user_client.get_chat(group_identifier)
            group_name = chat.title or str(group_identifier)

            if progress_callback:
                await progress_callback(f"📊 Accessing group: {group_name}")
                await progress_callback(f"👥 Total members: {chat.members_count}")
                await progress_callback(f"🔍 Acting as normal user to extract from messages")

            # Step 3: Extract users and messages from chunks
            users_data = []
            messages_data = []
            all_user_ids: Set[int] = set()

            if progress_callback:
                await progress_callback("📨 Starting to collect messages and extract users...")

            # Get messages in chunks to avoid limitations
            await self._extract_users_and_messages_from_chunks(
                group_identifier, users_data, messages_data, all_user_ids, chat, group_name, progress_callback
            )

            if progress_callback:
                await progress_callback(
                    f"✅ Extraction complete! Found {len(users_data)} unique users "
                    f"and {len(messages_data)} messages"
                )

            return users_data, group_name, messages_data

        except Exception as e:
            logger.error(f"Error in message-based extraction: {e}")
            raise
    
    async def _join_group_as_user(self, group_identifier: str, progress_callback=None):
        """
        Join the group as a normal user
        """
        if not self.user_client or not self.is_user_client_active:
            raise ValueError("User client not available for joining group")

        try:
            if progress_callback:
                await progress_callback("🚪 Attempting to join group as user...")

            # Try to join the group using USER client (not bot client)
            await self.user_client.join_chat(group_identifier)

            if progress_callback:
                await progress_callback("✅ Successfully joined the group as user!")

            # Small delay after joining
            await asyncio.sleep(2)

        except Exception as e:
            # It's okay if we can't join (already member, private group, etc.)
            logger.info(f"Could not join group (might already be member): {e}")
            if progress_callback:
                await progress_callback("ℹ️ Using existing access to the group")

            # Check if the error is still about bot methods - this means user client isn't working
            if "BOT_METHOD_INVALID" in str(e):
                raise ValueError(
                    "User client is not working properly - still getting bot method errors. "
                    "Please ensure user session was created successfully."
                )
    
    async def _extract_users_and_messages_from_chunks(self, group_identifier: str, users_data: List[Dict[str, Any]],
                                                     messages_data: List[Dict[str, Any]], all_user_ids: Set[int],
                                                     chat, group_name: str, progress_callback=None):
        """
        Extract users and messages by getting them in chunks
        """
        try:
            chunk_size = 200  # Increased chunk size for better efficiency
            total_messages_processed = 0
            total_users_found = 0
            offset_id = 0
            max_messages = 100000  # Increased to 100k messages
            consecutive_empty_chunks = 0
            last_message_id = None

            if progress_callback:
                await progress_callback("📨 Starting COMPREHENSIVE message extraction (up to 100k messages)...")

            # First, try to get the total message count
            try:
                # Get one message to check if we have access
                test_messages = []
                async for msg in self.user_client.get_chat_history(group_identifier, limit=1):
                    test_messages.append(msg)

                if test_messages and progress_callback:
                    await progress_callback(f"✅ Message access confirmed. Starting full extraction...")
            except Exception as e:
                logger.warning(f"Could not test message access: {e}")

            while total_messages_processed < max_messages:
                try:
                    if progress_callback:
                        await progress_callback(f"📨 Getting message chunk (processed: {total_messages_processed})...")
                    
                    # Get messages chunk using user client with better pagination
                    messages = []
                    try:
                        # Use different approaches to get ALL messages
                        if offset_id == 0:
                            # First chunk - get most recent messages
                            async for message in self.user_client.get_chat_history(
                                group_identifier,
                                limit=chunk_size
                            ):
                                messages.append(message)
                        else:
                            # Subsequent chunks - use offset to get older messages
                            # Try multiple methods to ensure we get all messages
                            async for message in self.user_client.get_chat_history(
                                group_identifier,
                                limit=chunk_size,
                                offset_id=offset_id
                            ):
                                messages.append(message)

                        # If we got fewer messages than expected, try alternative method
                        if len(messages) < chunk_size and offset_id > 0:
                            # Try getting messages with offset_date instead
                            if messages:
                                last_date = messages[-1].date
                                additional_messages = []
                                async for message in self.user_client.get_chat_history(
                                    group_identifier,
                                    limit=chunk_size,
                                    offset_date=last_date
                                ):
                                    # Avoid duplicates
                                    if message.id not in [m.id for m in messages]:
                                        additional_messages.append(message)

                                messages.extend(additional_messages[:chunk_size//2])  # Add up to half chunk size

                    except Exception as e:
                        logger.warning(f"Error getting messages chunk: {e}")
                        consecutive_empty_chunks += 1
                        if consecutive_empty_chunks >= 3:
                            if progress_callback:
                                await progress_callback("❌ Multiple failed attempts, stopping extraction")
                            break
                        continue

                    if not messages:
                        consecutive_empty_chunks += 1
                        if progress_callback:
                            await progress_callback(f"📭 Empty chunk #{consecutive_empty_chunks}")

                        # If we get 3 consecutive empty chunks, we've reached the end
                        if consecutive_empty_chunks >= 3:
                            if progress_callback:
                                await progress_callback("📭 Reached end of message history")
                            break
                        continue

                    # Reset empty chunk counter since we got messages
                    consecutive_empty_chunks = 0
                    
                    # Process messages in this chunk
                    chunk_users_found = 0
                    chunk_messages_saved = 0

                    for message in messages:
                        try:
                            # Always save message data
                            message_data = self._extract_message_data(message, chat.id, group_name)
                            messages_data.append(message_data)
                            chunk_messages_saved += 1

                            # Extract user from message sender
                            if message.from_user and message.from_user.id not in all_user_ids:
                                user_data = self._extract_user_data_from_message(message)
                                users_data.append(user_data)
                                all_user_ids.add(message.from_user.id)
                                chunk_users_found += 1
                                total_users_found += 1

                            # Extract user from forwarded message
                            if (hasattr(message, 'forward_from') and message.forward_from and
                                message.forward_from.id not in all_user_ids):
                                user_data = self._extract_user_data_from_forward(message.forward_from)
                                users_data.append(user_data)
                                all_user_ids.add(message.forward_from.id)
                                chunk_users_found += 1
                                total_users_found += 1

                            # Extract users from replies
                            if (message.reply_to_message and message.reply_to_message.from_user and
                                message.reply_to_message.from_user.id not in all_user_ids):
                                user_data = self._extract_user_data_from_message(message.reply_to_message)
                                users_data.append(user_data)
                                all_user_ids.add(message.reply_to_message.from_user.id)
                                chunk_users_found += 1
                                total_users_found += 1

                        except Exception as e:
                            logger.warning(f"Error processing message: {e}")
                            continue
                    
                    total_messages_processed += len(messages)

                    if progress_callback:
                        await progress_callback(
                            f"📨 Chunk complete: {len(messages)} messages saved, "
                            f"{chunk_users_found} new users (total users: {total_users_found}, total messages: {len(messages_data)})"
                        )

                    # Update offset for next chunk - use the ID of the last message
                    if messages:
                        # Set offset to the last message ID to get older messages
                        offset_id = messages[-1].id

                        # Log progress every 1000 messages
                        if total_messages_processed % 1000 == 0 and progress_callback:
                            await progress_callback(
                                f"🔄 Progress: {total_messages_processed} messages processed, "
                                f"{total_users_found} users found"
                            )

                    # Small delay between chunks to avoid rate limiting
                    await asyncio.sleep(0.5)

                    # If we got fewer messages than chunk_size, we might be near the end
                    # But continue until we get consecutive empty chunks
                    if len(messages) < chunk_size:
                        if progress_callback:
                            await progress_callback(f"📉 Smaller chunk received ({len(messages)} messages), continuing...")
                        # Don't break here - let the empty chunk detection handle the end
                
                except FloodWait as e:
                    if progress_callback:
                        await progress_callback(f"⏳ Rate limited. Waiting {e.value} seconds...")
                    await asyncio.sleep(e.value)
                    continue
                    
                except Exception as e:
                    logger.error(f"Error in chunk processing: {e}")
                    break
            
            if progress_callback:
                await progress_callback(
                    f"✅ **Comprehensive Message Extraction Complete!**\n"
                    f"📊 **Final Statistics:**\n"
                    f"• Total Messages Processed: {total_messages_processed:,}\n"
                    f"• Unique Users Found: {total_users_found:,}\n"
                    f"• Message Chunks: {(total_messages_processed // chunk_size) + 1}\n"
                    f"• Max Messages Limit: {max_messages:,}"
                )
                
        except Exception as e:
            logger.error(f"Error in message chunk extraction: {e}")
            raise
    
    def _extract_user_data_from_message(self, message: Message) -> Dict[str, Any]:
        """
        Extract user data from a message sender
        """
        user = message.from_user
        
        return {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
            'status': 'MEMBER',  # From message, so assume member
            'joined_date': None,
            'is_member': True,
            'last_online_date': None,
            'is_admin': False,
            'source': 'message',  # Mark that this user was found from messages
            'message_date': message.date.isoformat() if message.date else None,
        }
    
    def _extract_user_data_from_forward(self, user) -> Dict[str, Any]:
        """
        Extract user data from a forwarded message sender
        """
        return {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
            'status': 'UNKNOWN',  # From forward, status unknown
            'joined_date': None,
            'is_member': False,  # Might not be a member
            'last_online_date': None,
            'is_admin': False,
            'source': 'forwarded_message',  # Mark that this user was found from forwarded messages
            'message_date': None,
        }

    def _extract_message_data(self, message: Message, chat_id: int, chat_title: str) -> Dict[str, Any]:
        """
        Extract comprehensive message data
        """
        # Basic message info
        message_data = {
            'message_id': message.id,
            'date': message.date.isoformat() if message.date else None,
            'chat_id': chat_id,
            'chat_title': chat_title,
            'message_text': message.text or message.caption or '',
            'message_type': self._get_message_type(message),
            'media_type': self._get_media_type(message),
            'views': getattr(message, 'views', None),
            'edit_date': message.edit_date.isoformat() if getattr(message, 'edit_date', None) else None,
        }

        # Sender info
        if message.from_user:
            message_data.update({
                'user_id': message.from_user.id,
                'username': message.from_user.username,
                'first_name': message.from_user.first_name,
                'last_name': message.from_user.last_name,
                'is_bot': message.from_user.is_bot,
            })
        else:
            message_data.update({
                'user_id': None,
                'username': None,
                'first_name': None,
                'last_name': None,
                'is_bot': False,
            })

        # Forward info
        if hasattr(message, 'forward_from') and message.forward_from:
            message_data.update({
                'is_forwarded': True,
                'forward_from_user_id': message.forward_from.id,
                'forward_from_username': message.forward_from.username,
                'forward_from_first_name': message.forward_from.first_name,
                'forward_from_last_name': message.forward_from.last_name,
            })
        elif hasattr(message, 'forward_from_chat') and message.forward_from_chat:
            message_data.update({
                'is_forwarded': True,
                'forward_from_user_id': None,
                'forward_from_username': None,
                'forward_from_first_name': message.forward_from_chat.title,
                'forward_from_last_name': None,
            })
        else:
            message_data.update({
                'is_forwarded': False,
                'forward_from_user_id': None,
                'forward_from_username': None,
                'forward_from_first_name': None,
                'forward_from_last_name': None,
            })

        # Reply info
        if message.reply_to_message:
            reply_msg = message.reply_to_message
            message_data.update({
                'reply_to_message_id': reply_msg.id,
                'reply_to_user_id': reply_msg.from_user.id if reply_msg.from_user else None,
                'reply_to_username': reply_msg.from_user.username if reply_msg.from_user else None,
            })
        else:
            message_data.update({
                'reply_to_message_id': None,
                'reply_to_user_id': None,
                'reply_to_username': None,
            })

        return message_data

    def _get_message_type(self, message: Message) -> str:
        """Get the type of message"""
        if message.text:
            return 'text'
        elif message.photo:
            return 'photo'
        elif message.video:
            return 'video'
        elif message.audio:
            return 'audio'
        elif message.voice:
            return 'voice'
        elif message.document:
            return 'document'
        elif message.sticker:
            return 'sticker'
        elif message.animation:
            return 'animation'
        elif message.location:
            return 'location'
        elif message.contact:
            return 'contact'
        elif message.poll:
            return 'poll'
        elif message.venue:
            return 'venue'
        elif message.dice:
            return 'dice'
        elif message.video_note:
            return 'video_note'
        else:
            return 'other'

    def _get_media_type(self, message: Message) -> str:
        """Get the media type if any"""
        if message.photo:
            return 'photo'
        elif message.video:
            return 'video'
        elif message.audio:
            return 'audio'
        elif message.voice:
            return 'voice'
        elif message.document:
            return 'document'
        elif message.sticker:
            return 'sticker'
        elif message.animation:
            return 'gif'
        elif message.video_note:
            return 'video_note'
        else:
            return None

    async def _download_user_profile_photo(self, user, user_id: int) -> str:
        """
        Download user profile photo and return the file path
        """
        try:
            if not self.user_client or not self.is_user_client_active:
                return None

            # Create filename
            username = user.username or f"user_{user_id}"
            filename = f"{username}_{user_id}.jpg"
            filepath = os.path.join(self.profile_images_dir, filename)

            # Skip if already downloaded
            if os.path.exists(filepath):
                return filepath

            # Download profile photo
            photo_path = await self.user_client.download_media(
                user.photo.big_file_id if user.photo else None,
                file_name=filepath
            )

            if photo_path:
                logger.info(f"Downloaded profile photo for user {user_id}: {photo_path}")
                return photo_path
            else:
                return None

        except Exception as e:
            logger.warning(f"Could not download profile photo for user {user_id}: {e}")
            return None

    async def stop_user_client(self):
        """
        Stop the user client
        """
        if self.user_client and self.is_user_client_active:
            await self.user_client.stop()
            self.is_user_client_active = False
            logger.info("User client stopped")

"""
Message-based user extractor that acts like a normal user
Joins group and extracts users from messages without using get_chat_history
"""

from pyrogram import Client
from pyrogram.types import Message
from pyrogram.errors import FloodWait, Chat<PERSON>dminRequired, PeerIdInvalid, ChannelPrivate
from typing import List, Dict, Any, Set
import asyncio
import logging
from config import API_ID, API_HASH

logger = logging.getLogger(__name__)


class MessageUserExtractor:
    """
    Extracts users by acting like a normal user and getting messages
    """
    
    def __init__(self, bot_client: Client):
        self.bot_client = bot_client
        self.user_client = None
        self.is_user_client_active = False
    
    async def create_user_session(self, session_name: str = "message_extractor_user"):
        """
        Create a user session that acts like a normal user
        """
        try:
            # Stop any existing user client
            if self.user_client and self.is_user_client_active:
                await self.user_client.stop()

            # Create new user client (NOT bot client)
            self.user_client = Client(
                session_name,
                api_id=API_ID,
                api_hash=API_HASH,
                # No bot_token - this makes it a user client
            )

            # Start user client (will prompt for phone if needed)
            await self.user_client.start()
            me = await self.user_client.get_me()
            self.is_user_client_active = True
            logger.info(f"User client started: {me.first_name} (@{me.username if me.username else 'no_username'})")
            return True

        except Exception as e:
            logger.error(f"Failed to create user session: {e}")
            self.is_user_client_active = False
            if self.user_client:
                try:
                    await self.user_client.stop()
                except:
                    pass
                self.user_client = None
            return False
    
    async def extract_users_from_messages(self, group_identifier: str, progress_callback=None) -> tuple[List[Dict[str, Any]], str]:
        """
        Extract users by joining group and getting messages like a normal user
        
        Args:
            group_identifier: Group username (@groupname) or ID (-100123456789)
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Tuple of (users_data, group_name)
        """
        if not self.is_user_client_active:
            raise ValueError("User client not active. Call create_user_session first.")
        
        try:
            # Step 1: Try to join the group as a user
            await self._join_group_as_user(group_identifier, progress_callback)
            
            # Step 2: Get group info
            chat = await self.user_client.get_chat(group_identifier)
            group_name = chat.title or str(group_identifier)
            
            if progress_callback:
                await progress_callback(f"📊 Joined group: {group_name}")
                await progress_callback(f"👥 Total members: {chat.members_count}")
                await progress_callback(f"🔍 Acting as normal user to extract from messages")
            
            # Step 3: Extract users from messages in chunks
            users_data = []
            all_user_ids: Set[int] = set()
            
            if progress_callback:
                await progress_callback("📨 Starting to collect messages and extract users...")
            
            # Get messages in chunks to avoid limitations
            await self._extract_users_from_message_chunks(
                group_identifier, users_data, all_user_ids, progress_callback
            )
            
            if progress_callback:
                await progress_callback(f"✅ Extraction complete! Found {len(users_data)} unique users from messages")
            
            return users_data, group_name
            
        except Exception as e:
            logger.error(f"Error in message-based extraction: {e}")
            raise
    
    async def _join_group_as_user(self, group_identifier: str, progress_callback=None):
        """
        Join the group as a normal user
        """
        try:
            if progress_callback:
                await progress_callback("🚪 Joining group as user...")
            
            # Try to join the group
            await self.user_client.join_chat(group_identifier)
            
            if progress_callback:
                await progress_callback("✅ Successfully joined the group as user!")
            
            # Small delay after joining
            await asyncio.sleep(2)
                
        except Exception as e:
            # It's okay if we can't join (already member, private group, etc.)
            logger.info(f"Could not join group (might already be member): {e}")
            if progress_callback:
                await progress_callback("ℹ️ Using existing access to the group")
    
    async def _extract_users_from_message_chunks(self, group_identifier: str, users_data: List[Dict[str, Any]], 
                                                all_user_ids: Set[int], progress_callback=None):
        """
        Extract users from messages by getting them in chunks
        """
        try:
            chunk_size = 100  # Get messages in chunks of 100
            total_messages_processed = 0
            total_users_found = 0
            offset_id = 0
            max_messages = 10000  # Maximum messages to process
            
            while total_messages_processed < max_messages:
                try:
                    if progress_callback:
                        await progress_callback(f"📨 Getting message chunk (processed: {total_messages_processed})...")
                    
                    # Get messages chunk using user client (not bot client)
                    messages = []
                    try:
                        # Use user client to get chat history (this works for users)
                        if offset_id == 0:
                            # First chunk - get most recent messages
                            async for message in self.user_client.get_chat_history(
                                group_identifier,
                                limit=chunk_size
                            ):
                                messages.append(message)
                        else:
                            # Subsequent chunks - use offset
                            async for message in self.user_client.get_chat_history(
                                group_identifier,
                                limit=chunk_size,
                                offset_id=offset_id
                            ):
                                messages.append(message)

                    except Exception as e:
                        logger.warning(f"Error getting messages chunk: {e}")
                        # If we can't get more messages, break the loop
                        break
                    
                    if not messages:
                        if progress_callback:
                            await progress_callback("📭 No more messages to process")
                        break
                    
                    # Process messages in this chunk
                    chunk_users_found = 0
                    for message in messages:
                        try:
                            # Extract user from message sender
                            if message.from_user and message.from_user.id not in all_user_ids:
                                user_data = self._extract_user_data_from_message(message)
                                users_data.append(user_data)
                                all_user_ids.add(message.from_user.id)
                                chunk_users_found += 1
                                total_users_found += 1
                            
                            # Extract user from forwarded message
                            if (hasattr(message, 'forward_from') and message.forward_from and 
                                message.forward_from.id not in all_user_ids):
                                user_data = self._extract_user_data_from_forward(message.forward_from)
                                users_data.append(user_data)
                                all_user_ids.add(message.forward_from.id)
                                chunk_users_found += 1
                                total_users_found += 1
                            
                            # Extract users from replies
                            if (message.reply_to_message and message.reply_to_message.from_user and 
                                message.reply_to_message.from_user.id not in all_user_ids):
                                user_data = self._extract_user_data_from_message(message.reply_to_message)
                                users_data.append(user_data)
                                all_user_ids.add(message.reply_to_message.from_user.id)
                                chunk_users_found += 1
                                total_users_found += 1
                                
                        except Exception as e:
                            logger.warning(f"Error processing message: {e}")
                            continue
                    
                    total_messages_processed += len(messages)
                    
                    if progress_callback:
                        await progress_callback(
                            f"📨 Chunk complete: {len(messages)} messages, "
                            f"{chunk_users_found} new users (total: {total_users_found})"
                        )
                    
                    # Update offset for next chunk
                    if messages:
                        offset_id = messages[-1].id
                    
                    # Small delay between chunks to avoid rate limiting
                    await asyncio.sleep(1)
                    
                    # If we got fewer messages than chunk_size, we've reached the end
                    if len(messages) < chunk_size:
                        if progress_callback:
                            await progress_callback("📭 Reached end of available messages")
                        break
                
                except FloodWait as e:
                    if progress_callback:
                        await progress_callback(f"⏳ Rate limited. Waiting {e.value} seconds...")
                    await asyncio.sleep(e.value)
                    continue
                    
                except Exception as e:
                    logger.error(f"Error in chunk processing: {e}")
                    break
            
            if progress_callback:
                await progress_callback(
                    f"✅ Message processing complete! "
                    f"Processed {total_messages_processed} messages, found {total_users_found} unique users"
                )
                
        except Exception as e:
            logger.error(f"Error in message chunk extraction: {e}")
            raise
    
    def _extract_user_data_from_message(self, message: Message) -> Dict[str, Any]:
        """
        Extract user data from a message sender
        """
        user = message.from_user
        
        return {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
            'status': 'MEMBER',  # From message, so assume member
            'joined_date': None,
            'is_member': True,
            'last_online_date': None,
            'is_admin': False,
            'source': 'message',  # Mark that this user was found from messages
            'message_date': message.date.isoformat() if message.date else None,
        }
    
    def _extract_user_data_from_forward(self, user) -> Dict[str, Any]:
        """
        Extract user data from a forwarded message sender
        """
        return {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
            'status': 'UNKNOWN',  # From forward, status unknown
            'joined_date': None,
            'is_member': False,  # Might not be a member
            'last_online_date': None,
            'is_admin': False,
            'source': 'forwarded_message',  # Mark that this user was found from forwarded messages
            'message_date': None,
        }
    
    async def stop_user_client(self):
        """
        Stop the user client
        """
        if self.user_client and self.is_user_client_active:
            await self.user_client.stop()
            self.is_user_client_active = False
            logger.info("User client stopped")

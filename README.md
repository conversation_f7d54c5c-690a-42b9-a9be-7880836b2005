# Telegram Users Grabber Bot

A powerful Telegram bot that can extract user lists from Telegram groups and export them to CSV files.

## Features

- 🤖 **Easy to use**: Simple commands to extract user data
- 📊 **Comprehensive data**: Extracts usernames, names, IDs, admin status, and more
- 📁 **CSV Export**: Clean, organized CSV files with user statistics
- 🔄 **Progress tracking**: Real-time updates during extraction
- 🛡️ **Error handling**: Robust error handling and user feedback
- 📈 **Statistics**: Detailed statistics about extracted users

## Prerequisites

Before running the bot, you need:

1. **Telegram Bot Token**: Create a bot via [@BotFather](https://t.me/BotFather)
2. **Telegram API Credentials**: Get `api_id` and `api_hash` from [my.telegram.org/apps](https://my.telegram.org/apps)
3. **Python 3.8+**: Make sure you have Python installed

## Installation

1. **Clone or download this project**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up API credentials**:
   - Copy `.env.example` to `.env`
   - Edit `.env` and add your `API_ID` and `API_HASH`
   ```
   API_ID=your_api_id_here
   API_HASH=your_api_hash_here
   ```

4. **Update bot token** (if different):
   - Edit `config.py` and update the `BOT_TOKEN` if needed

## Usage

### 1. Start the bot
```bash
python main.py
```

### 2. Add bot to your target group
- Add the bot to the Telegram group you want to extract users from
- Give the bot **admin rights** (required to access member list)

### 3. Use the bot
Send these commands to the bot in a private chat:

- `/start` - Get welcome message and instructions
- `/help` - Get detailed help
- `/extract @groupname` - Extract users from a public group
- `/extract -1001234567890` - Extract users using group ID

### 4. Download CSV file
The bot will process the group and send you a CSV file with all user data.

## CSV File Contents

The exported CSV includes:

| Column | Description |
|--------|-------------|
| user_id | Unique Telegram user ID |
| username | Username (without @) |
| first_name | User's first name |
| last_name | User's last name |
| full_name | Combined first and last name |
| phone_number | Phone number (if visible) |
| is_bot | Whether the user is a bot |
| is_verified | Whether the user is verified |
| is_premium | Whether the user has Telegram Premium |
| status | Member status (MEMBER, ADMIN, etc.) |
| is_admin | Whether the user is an admin |
| last_online_date | Last time user was online |
| joined_date | When user joined the group |
| language_code | User's language code |
| dc_id | Telegram data center ID |

## Commands

- `/start` - Show welcome message
- `/help` - Show detailed help
- `/extract <group>` - Extract participants using bot method (fast, limited)
- `/extract_messages <group>` - Extract users from messages using user method (comprehensive)
- `/get_id <link_or_username>` - Get user/group/channel ID from Telegram link or username

## Examples

### Bot Method (Standard)
```
/extract @mychannel
/extract @mypublicgroup
/extract -1001234567890
```

### Message Method (Advanced)
```
/extract_messages @mychannel
/extract_messages @mypublicgroup
/extract_messages -1001234567890
```

### Get ID Command
```
/get_id https://t.me/username
/get_id @groupname
/get_id https://t.me/joinchat/ABC123DEF456
/get_id username
```

## Extraction Methods

### 1. Bot Method (`/extract`)
- ✅ **Fast and simple**
- ✅ **No phone verification required**
- ⚠️ **Limited to visible participants** (admins, bots, some recent members)
- ⚠️ **Restricted by Telegram's bot API limitations**

### 2. Message Method (`/extract_messages`)
- ✅ **Comprehensive user extraction**
- ✅ **Extracts users from message history**
- ✅ **Acts like a normal user**
- ✅ **Can find many more users**
- ⚠️ **Requires phone verification on first use**
- ⚠️ **Takes longer to process**

## Limitations

- Bot needs admin rights in the target group
- Some user information may not be available due to privacy settings
- Large groups (10k+ members) may take several minutes to process
- Rate limits may apply for very large groups
- Private groups require the exact group ID

## Troubleshooting

### Common Issues

**"Invalid group ID or username"**
- Make sure the bot is added to the group
- For private groups, use the exact group ID (starts with -100)

**"Bot needs admin rights"**
- Add the bot as admin in the target group
- Make sure it has permission to see members

**"Rate limited"**
- Wait for the specified time and try again
- This happens with very large groups

**"Cannot access private channel/group"**
- Make sure the bot is added as a member
- For private groups, you need the exact group ID

### Getting Group ID

For private groups, you can get the group ID by:
1. Adding the bot to the group
2. Forwarding any message from the group to [@userinfobot](https://t.me/userinfobot)
3. The bot will show you the group ID

## Security Notes

- Keep your API credentials secure
- Don't share your bot token
- Be respectful of user privacy
- Only extract users from groups you have permission to access

## Requirements

- Python 3.8+
- pyrogram
- tgcrypto
- python-dotenv
- pandas

## License

This project is for educational purposes. Please respect Telegram's Terms of Service and user privacy.

## Support

If you encounter any issues:
1. Check the troubleshooting section
2. Make sure all requirements are met
3. Verify your API credentials are correct
4. Check that the bot has proper permissions in the target group

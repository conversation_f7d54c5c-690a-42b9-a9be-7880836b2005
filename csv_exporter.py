import csv
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
import os
from config import CSV_FILENAME_TEMPLATE


class CSVExporter:
    """Handles exporting user data to CSV format"""
    
    def __init__(self):
        self.output_dir = "exports"
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """Create exports directory if it doesn't exist"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def export_users_to_csv(self, users_data: List[Dict[str, Any]], group_name: str) -> str:
        """
        Export users data to CSV file
        
        Args:
            users_data: List of user dictionaries
            group_name: Name of the group for filename
            
        Returns:
            str: Path to the created CSV file
        """
        # Clean group name for filename
        clean_group_name = self._clean_filename(group_name)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        filename = CSV_FILENAME_TEMPLATE.format(
            group_name=clean_group_name,
            timestamp=timestamp
        )
        filepath = os.path.join(self.output_dir, filename)
        
        # Convert to DataFrame for better handling
        df = pd.DataFrame(users_data)
        
        # Reorder columns for better readability
        column_order = [
            'user_id', 'username', 'first_name', 'last_name', 'full_name',
            'phone_number', 'is_bot', 'is_verified', 'is_premium', 'is_scam',
            'is_fake', 'is_restricted', 'status', 'last_online_date',
            'language_code', 'dc_id', 'profile_image_url', 'has_profile_photo',
            'source', 'message_date'
        ]
        
        # Only include columns that exist in the data
        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]
        
        # Export to CSV
        df.to_csv(filepath, index=False, encoding='utf-8')
        
        return filepath
    
    def _clean_filename(self, filename: str) -> str:
        """Clean filename by removing invalid characters"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename[:50]  # Limit length
    
    def get_user_stats(self, users_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate statistics about the extracted users
        
        Args:
            users_data: List of user dictionaries
            
        Returns:
            Dict with statistics
        """
        if not users_data:
            return {}
        
        df = pd.DataFrame(users_data)
        
        stats = {
            'total_users': len(df),
            'bots': len(df[df.get('is_bot', False) == True]) if 'is_bot' in df.columns else 0,
            'verified_users': len(df[df.get('is_verified', False) == True]) if 'is_verified' in df.columns else 0,
            'premium_users': len(df[df.get('is_premium', False) == True]) if 'is_premium' in df.columns else 0,
            'users_with_username': len(df[df['username'].notna()]) if 'username' in df.columns else 0,
            'users_with_phone': len(df[df['phone_number'].notna()]) if 'phone_number' in df.columns else 0,
            'users_with_profile_photo': len(df[df.get('has_profile_photo', False) == True]) if 'has_profile_photo' in df.columns else 0,
            'users_from_messages': len(df[df.get('source', '') == 'message']) if 'source' in df.columns else 0,
            'users_from_forwards': len(df[df.get('source', '') == 'forwarded_message']) if 'source' in df.columns else 0,
        }
        
        return stats

    def export_messages_to_csv(self, messages_data: List[Dict[str, Any]], group_name: str) -> str:
        """
        Export messages data to CSV file

        Args:
            messages_data: List of message dictionaries
            group_name: Name of the group for filename

        Returns:
            str: Path to the created CSV file
        """
        # Clean group name for filename
        clean_group_name = self._clean_filename(group_name)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        filename = f"telegram_messages_{clean_group_name}_{timestamp}.csv"
        filepath = os.path.join(self.output_dir, filename)

        # Convert to DataFrame for better handling
        df = pd.DataFrame(messages_data)

        # Reorder columns for better readability
        column_order = [
            'message_id', 'date', 'user_id', 'username', 'first_name', 'last_name',
            'message_text', 'message_type', 'media_type', 'is_forwarded', 'forward_from_user_id',
            'forward_from_username', 'reply_to_message_id', 'reply_to_user_id', 'reply_to_username',
            'views', 'edit_date', 'is_bot', 'chat_id', 'chat_title'
        ]

        # Only include columns that exist in the data
        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]

        # Export to CSV
        df.to_csv(filepath, index=False, encoding='utf-8')

        return filepath

    def get_message_stats(self, messages_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate statistics about the extracted messages

        Args:
            messages_data: List of message dictionaries

        Returns:
            Dict with statistics
        """
        if not messages_data:
            return {}

        df = pd.DataFrame(messages_data)

        stats = {
            'total_messages': len(df),
            'text_messages': len(df[df.get('message_type', '') == 'text']) if 'message_type' in df.columns else 0,
            'media_messages': len(df[df.get('media_type', '').notna()]) if 'media_type' in df.columns else 0,
            'forwarded_messages': len(df[df.get('is_forwarded', False) == True]) if 'is_forwarded' in df.columns else 0,
            'reply_messages': len(df[df.get('reply_to_message_id', '').notna()]) if 'reply_to_message_id' in df.columns else 0,
            'unique_users': len(df['user_id'].unique()) if 'user_id' in df.columns else 0,
            'bot_messages': len(df[df.get('is_bot', False) == True]) if 'is_bot' in df.columns else 0,
        }

        # Date range
        if 'date' in df.columns:
            try:
                df['date'] = pd.to_datetime(df['date'])
                stats['date_range_start'] = df['date'].min().strftime('%Y-%m-%d %H:%M:%S')
                stats['date_range_end'] = df['date'].max().strftime('%Y-%m-%d %H:%M:%S')
            except Exception:
                stats['date_range_start'] = 'Unknown'
                stats['date_range_end'] = 'Unknown'

        return stats

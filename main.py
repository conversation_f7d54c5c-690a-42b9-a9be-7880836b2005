import asyncio
import logging
import os
from pyrogram import Client, filters
from pyrogram.types import Message
from pyrogram.errors import ApiIdInvalid, ApiIdPublishedFlood, AccessTokenInvalid

from config import BOT_TOKEN, API_ID, API_HASH, SESSION_NAME, COMMANDS
from user_extractor import UserExtractor
from message_user_extractor import MessageUserExtractor
from csv_exporter import CSVExporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize components
csv_exporter = CSVExporter()


class TelegramUsersGrabberBot:
    """Main bot class for Telegram Users Grabber"""
    
    def __init__(self):
        if not API_ID or not API_HASH:
            raise ValueError(
                "API_ID and API_HASH are required. Please create a .env file with your credentials.\n"
                "Get them from https://my.telegram.org/apps"
            )
        
        self.app = Client(
            SESSION_NAME,
            api_id=API_ID,
            api_hash=API_HASH,
            bot_token=BOT_TOKEN
        )
        
        self.user_extractor = UserExtractor(self.app)
        self.message_extractor = MessageUserExtractor(self.app)
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup message handlers"""
        
        @self.app.on_message(filters.command("start"))
        async def start_command(client, message: Message):
            welcome_text = """
🤖 **Telegram Users Grabber Bot**

This bot can extract user lists from Telegram groups and export them to CSV files.

**Available Commands:**
/start - Show this welcome message
/help - Show detailed help
/extract <group_id_or_username> - Extract available participants (bot method)
/extract_messages <group_id_or_username> - Extract users from messages (user method)
/get_id <telegram_link> - Get user/group/channel ID from Telegram link

**Usage Examples:**
• `/extract @mygroup` - Extract participants using bot method (public groups)
• `/extract_messages @mygroup` - Extract users from messages (more comprehensive)
• `/extract_messages https://t.me/+ABC123` - Extract from private invite links
• `/extract -1001234567890` - Extract from group using ID
• `/get_id https://t.me/username` - Get ID from any Telegram link
• `/get_id @username` - Get ID from username

**Requirements:**
1. The bot must be added to the target group
2. The bot needs admin rights to access member lists
3. For private groups, you need the exact group ID

**Important Limitations (Telegram's Bot Restrictions):**
• **Large Groups/Channels**: Bots can only see admins, bots, and some recent members
• **Private Groups**: Very limited access for bots
• **Small Groups** (< 200 members): Usually better access
• **Message History**: Bots cannot access message history in most groups
• **Best Results**: Use with small, public groups where bot has admin rights

Type /help for more detailed instructions.
            """
            await message.reply_text(welcome_text)
        
        @self.app.on_message(filters.command("help"))
        async def help_command(client, message: Message):
            help_text = """
📖 **Detailed Help - Telegram Users Grabber Bot**

**How to use this bot:**

1️⃣ **Add bot to your group:**
   - Add this bot to the target group
   - Give it admin rights (needed to access member list)

2️⃣ **Choose extraction method:**
   - **Bot Method** (`/extract`): Fast, limited to visible participants
   - **Message Method** (`/extract_messages`): Comprehensive, extracts from messages

3️⃣ **Get group information:**
   - For public groups: Use @username (e.g., @mygroup)
   - For private groups: Use group ID (e.g., -1001234567890)

4️⃣ **Get IDs from links (optional):**
   - Use `/get_id https://t.me/groupname` to get group ID
   - Use `/get_id @username` to get user/group ID
   - Copy the ID for use in extraction commands

5️⃣ **Extract users:**
   - **Standard:** `/extract @groupname` (bot method)
   - **Advanced:** `/extract_messages @groupname` (user method, requires phone verification)
   - **With ID:** `/extract -1001234567890` (using ID from get_id command)
   - Wait for the process to complete
   - Download the CSV file when ready

**CSV File Contents:**
The exported CSV will include:
• User ID, Username, First/Last Name
• Phone number (if visible)
• Bot status, Verification status
• Premium status, Admin status
• Last online date, Join date
• And more user information

**Important Limitations (Telegram Bot API Restrictions):**

**Bot Method (`/extract`) - What Bots Can Do:**
• ✅ Access public groups and channels
• ✅ See administrators and their permissions
• ✅ See bots in the group
• ✅ See some recently active members (limited)
• ✅ Work best with small groups (< 200 members)

**Bot Method - What Bots Cannot Do:**
• ❌ Access private invite links (https://t.me/+ABC123)
• ❌ Access full member lists in large groups/channels
• ❌ Read message history in most groups
• ❌ Join groups via invite links

**Message Method (`/extract_messages`) - Enhanced Access:**
• ✅ Can access private invite links
• ✅ Can read message history
• ✅ Can extract users from messages
• ✅ Much more comprehensive results
• ⚠️ Requires phone verification on first use

**Recommendation:**
• Use `/extract` for public groups where bot is admin
• Use `/extract_messages` for private groups or comprehensive extraction

**Troubleshooting:**
• "Invalid group" - Make sure bot is added to the group
• "Admin required" - Give bot admin rights
• "Rate limited" - Wait and try again later

Need more help? Contact the bot developer.
            """
            await message.reply_text(help_text)
        
        @self.app.on_message(filters.command("extract"))
        async def extract_command(client, message: Message):
            # Check if group identifier is provided
            if len(message.command) < 2:
                await message.reply_text(
                    "❌ Please provide a group identifier.\n\n"
                    "**Usage:** `/extract <group_id_or_username>`\n\n"
                    "**Examples:**\n"
                    "• `/extract @mygroup`\n"
                    "• `/extract -1001234567890`\n"
                    "• `/extract https://t.me/mygroup`\n"
                    "• `/extract https://t.me/+ABC123` (private invite links)\n"
                    "• `/extract +ABC123` (private invite hash)\n\n"
                    "**Note:** This method uses a user session and can access private groups."
                )
                return
            
            group_identifier = message.command[1]
            user_id = message.from_user.id
            
            # Send initial status message
            status_message = await message.reply_text("🔄 Starting extraction process...")
            
            try:
                # Progress callback function
                async def progress_callback(status_text):
                    try:
                        await status_message.edit_text(status_text)
                    except Exception:
                        pass  # Ignore edit errors
                
                # Use message extractor (user session) for better access
                if not self.message_extractor.is_user_client_active:
                    await progress_callback("📱 Creating user session...")
                    await self.message_extractor.create_user_session()

                # Extract users using user session (same as extract_messages but users only)
                users_data, group_name = await self.message_extractor.extract_users_only(
                    group_identifier, progress_callback
                )
                
                if not users_data:
                    await status_message.edit_text("❌ No users found in the group.")
                    return
                
                # Update status
                await progress_callback("📊 Generating CSV file...")
                
                # Export to CSV
                csv_filepath = csv_exporter.export_users_to_csv(users_data, group_name)
                
                # Get statistics
                stats = csv_exporter.get_user_stats(users_data)
                
                # Prepare statistics message
                stats_text = f"""
✅ **Extraction Complete!**

📊 **Statistics:**
• Total Users: {stats.get('total_users', 0)}
• Bots: {stats.get('bots', 0)}
• Verified Users: {stats.get('verified_users', 0)}
• Premium Users: {stats.get('premium_users', 0)}
• Users with Username: {stats.get('users_with_username', 0)}
• Users with Phone: {stats.get('users_with_phone', 0)}

📁 **Group:** {group_name}
📄 **File:** {os.path.basename(csv_filepath)}
🔍 **Method:** User session extraction (can access private groups)
                """
                
                # Send CSV file
                await message.reply_document(
                    document=csv_filepath,
                    caption=stats_text
                )
                
                # Clean up status message
                await status_message.delete()
                
                # Clean up CSV file after sending
                try:
                    os.remove(csv_filepath)
                except Exception:
                    pass

                # Stop user client
                await self.message_extractor.stop_user_client()

            except Exception as e:
                error_message = f"❌ **Error:** {str(e)}"
                await status_message.edit_text(error_message)
                logger.error(f"Extraction error for user {user_id}: {e}")

                # Try to stop user client on error
                try:
                    await self.message_extractor.stop_user_client()
                except Exception:
                    pass

        @self.app.on_message(filters.command("extract_messages"))
        async def extract_messages_command(client, message: Message):
            # Check if group identifier is provided
            if len(message.command) < 2:
                await message.reply_text(
                    "❌ Please provide a group ID or username.\n\n"
                    "**Usage:**\n"
                    "• `/extract_messages @groupname`\n"
                    "• `/extract_messages -1001234567890`\n\n"
                    "**Note:** This method acts like a user and extracts users from messages. "
                    "It requires phone verification on first use."
                )
                return

            group_identifier = message.command[1]
            user_id = message.from_user.id

            # Send initial status message
            status_message = await message.reply_text("🔄 Initializing user session for message extraction...")

            try:
                # Initialize user session
                await status_message.edit_text(
                    "📱 **Setting up user session...**\n\n"
                    "⚠️ **IMPORTANT:** This method creates a USER session (not bot)\n"
                    "• If this is your first time, you'll need to verify your phone number\n"
                    "• Check your terminal/console for phone number prompt\n"
                    "• Enter your phone number and SMS verification code when prompted\n\n"
                    "🔄 Initializing..."
                )

                success = await self.message_extractor.create_user_session(f"user_session_{user_id}")
                if not success:
                    await status_message.edit_text(
                        "❌ **Failed to create user session**\n\n"
                        "**Possible reasons:**\n"
                        "• Phone verification failed or was cancelled\n"
                        "• Invalid API credentials (check .env file)\n"
                        "• Network connection issues\n"
                        "• Session creation was interrupted\n\n"
                        "**To fix:**\n"
                        "• Make sure you have valid API_ID and API_HASH in .env file\n"
                        "• Check your terminal/console for any prompts\n"
                        "• Complete phone verification when prompted\n"
                        "• Try the command again\n\n"
                        "**Debug:** Check the bot logs for more details."
                    )
                    return

                # Verify user session is actually working
                await status_message.edit_text("🔍 Verifying user session...")
                try:
                    # Test if user client is working
                    if not self.message_extractor.is_user_client_active:
                        await status_message.edit_text(
                            "❌ **User session not active**\n\n"
                            "The user session was created but is not active. "
                            "Please try again and ensure phone verification completes successfully."
                        )
                        return
                except Exception as e:
                    await status_message.edit_text(f"❌ **User session verification failed:** {str(e)}")
                    return

                # Progress callback function
                async def progress_callback(status_text):
                    try:
                        await status_message.edit_text(status_text)
                    except Exception:
                        pass  # Ignore edit errors

                # Extract users and messages
                users_data, group_name, messages_data = await self.message_extractor.extract_users_from_messages(
                    group_identifier, progress_callback
                )

                if not users_data and not messages_data:
                    await status_message.edit_text("❌ No users or messages found in the group.")
                    return

                # Update status
                await progress_callback("📊 Generating CSV files...")

                # Export users to CSV
                users_csv_filepath = csv_exporter.export_users_to_csv(users_data, group_name)

                # Export messages to CSV
                messages_csv_filepath = csv_exporter.export_messages_to_csv(messages_data, group_name)

                # Get statistics
                user_stats = csv_exporter.get_user_stats(users_data)
                message_stats = csv_exporter.get_message_stats(messages_data)

                # Prepare statistics message
                stats_text = f"""
✅ **Comprehensive Message Extraction Complete!**

👥 **User Statistics:**
• Total Users: {user_stats.get('total_users', 0)}
• Bots: {user_stats.get('bots', 0)}
• Verified Users: {user_stats.get('verified_users', 0)}
• Premium Users: {user_stats.get('premium_users', 0)}
• Users with Username: {user_stats.get('users_with_username', 0)}
• Users with Profile Photo: {user_stats.get('users_with_profile_photo', 0)}
• Users from Messages: {user_stats.get('users_from_messages', 0)}
• Users from Forwards: {user_stats.get('users_from_forwards', 0)}

📨 **Message Statistics:**
• Total Messages: {message_stats.get('total_messages', 0)}
• Text Messages: {message_stats.get('text_messages', 0)}
• Media Messages: {message_stats.get('media_messages', 0)}
• Forwarded Messages: {message_stats.get('forwarded_messages', 0)}
• Reply Messages: {message_stats.get('reply_messages', 0)}
• Date Range: {message_stats.get('date_range_start', 'Unknown')} to {message_stats.get('date_range_end', 'Unknown')}

📁 **Group:** {group_name}
🔍 **Method:** Message-based extraction (user session)
                """

                # Send both CSV files
                await message.reply_document(
                    document=users_csv_filepath,
                    caption=f"👥 **Users CSV File**\n{stats_text}"
                )

                await message.reply_document(
                    document=messages_csv_filepath,
                    caption=f"📨 **Messages CSV File**\n\n📄 **File:** {os.path.basename(messages_csv_filepath)}\n📊 **Contains:** All {message_stats.get('total_messages', 0)} messages with full details"
                )

                # Clean up status message
                await status_message.delete()

                # Clean up CSV files after sending
                try:
                    os.remove(users_csv_filepath)
                    os.remove(messages_csv_filepath)
                except Exception:
                    pass

                # Stop user client
                await self.message_extractor.stop_user_client()

            except Exception as e:
                error_message = f"❌ **Error:** {str(e)}"
                await status_message.edit_text(error_message)
                logger.error(f"Message extraction error for user {user_id}: {e}")

                # Try to stop user client on error
                try:
                    await self.message_extractor.stop_user_client()
                except Exception:
                    pass

        @self.app.on_message(filters.command("get_id"))
        async def get_id_command(client, message: Message):
            # Check if link/username is provided
            if len(message.command) < 2:
                await message.reply_text(
                    "❌ Please provide a Telegram link or username.\n\n"
                    "**Usage:**\n"
                    "• `/get_id https://t.me/username`\n"
                    "• `/get_id @username`\n"
                    "• `/get_id https://t.me/joinchat/ABC123`\n"
                    "• `/get_id username` (without @)\n\n"
                    "**Supported links:**\n"
                    "• User profiles: https://t.me/username\n"
                    "• Groups: https://t.me/groupname\n"
                    "• Channels: https://t.me/channelname\n"
                    "• Private groups: https://t.me/joinchat/...\n"
                    "• Direct usernames: @username or username"
                )
                return

            link_or_username = message.command[1]
            user_id = message.from_user.id

            # Send initial status message
            status_message = await message.reply_text("🔍 Resolving Telegram link/username...")

            try:
                # Parse and resolve the link/username
                resolved_info = await self._resolve_telegram_link(link_or_username)

                if not resolved_info:
                    await status_message.edit_text(
                        "❌ **Could not resolve the provided link/username**\n\n"
                        "**Possible reasons:**\n"
                        "• Invalid or malformed link\n"
                        "• Private chat/group that bot cannot access\n"
                        "• Username does not exist\n"
                        "• Link has expired (for joinchat links)\n\n"
                        "**Please check:**\n"
                        "• Make sure the link is correct\n"
                        "• For private groups, the bot needs to be added first\n"
                        "• Try using the username directly (e.g., @username)"
                    )
                    return

                # Format the response based on whether it's accessible
                if resolved_info.get('joined_via_link') == False and 'error' in resolved_info:
                    # Private group that couldn't be accessed
                    response_text = f"""
⚠️ **Private Group Link Detected**

🔗 **Input:** `{link_or_username}`

📊 **Information:**
• **Type:** {resolved_info['type']}
• **Status:** Not accessible to bot
• **Reason:** {resolved_info.get('error', 'Unknown error')}

🔧 **What this means:**
• This is a private group invite link
• The bot cannot access this group without being added
• You need to manually add the bot to the group first

📋 **To get the ID:**
1. Add this bot to the private group
2. Use `/get_id` with the group username (if it has one)
3. Or use `/extract` directly with the invite link after adding the bot

💡 **Alternative:** Try adding the bot to the group, then use `/extract {link_or_username}`
                    """
                else:
                    # Successfully resolved
                    usage_id = resolved_info['id'] if resolved_info['id'] != 'Unknown' else link_or_username
                    response_text = f"""
✅ **Link/Username Resolved Successfully!**

🔗 **Input:** `{link_or_username}`

📊 **Information:**
• **Type:** {resolved_info['type']}
• **ID:** `{resolved_info['id']}`
• **Title/Name:** {resolved_info['title']}
• **Username:** {resolved_info['username'] or 'None'}"""

                    if resolved_info.get('members_count'):
                        response_text += f"\n• **Members:** {resolved_info['members_count']:,}"

                    if resolved_info.get('joined_via_link'):
                        response_text += f"\n• **Status:** Successfully joined via invite link"

                    response_text += f"""

📋 **Usage:**
• For extraction: `/extract {usage_id}`
• For message extraction: `/extract_messages {usage_id}`

💡 **Tip:** Copy the ID above to use with other commands!
                    """

                await status_message.edit_text(response_text)

            except Exception as e:
                error_message = f"❌ **Error resolving link:** {str(e)}"
                await status_message.edit_text(error_message)
                logger.error(f"Get ID error for user {user_id}: {e}")

    async def _resolve_telegram_link(self, link_or_username: str) -> dict:
        """
        Resolve a Telegram link or username to get ID and information

        Args:
            link_or_username: Telegram link or username

        Returns:
            Dict with resolved information or None if failed
        """
        try:
            # Clean and parse the input
            identifier = self._parse_telegram_identifier(link_or_username)

            if not identifier:
                return None

            # Handle different types of identifiers
            if identifier.startswith('https://t.me/+') or identifier.startswith('https://t.me/joinchat/'):
                # Private invite link - try to get info by joining
                return await self._handle_private_invite_link(identifier)
            else:
                # Regular username or ID - use get_chat
                try:
                    chat = await self.app.get_chat(identifier)

                    # Determine the type
                    chat_type = "Unknown"
                    if hasattr(chat, 'type'):
                        if chat.type.name == "PRIVATE":
                            chat_type = "User"
                        elif chat.type.name == "GROUP":
                            chat_type = "Group"
                        elif chat.type.name == "SUPERGROUP":
                            chat_type = "Supergroup"
                        elif chat.type.name == "CHANNEL":
                            chat_type = "Channel"
                        elif chat.type.name == "BOT":
                            chat_type = "Bot"

                    # Get title/name
                    title = ""
                    if hasattr(chat, 'title') and chat.title:
                        title = chat.title
                    elif hasattr(chat, 'first_name') and chat.first_name:
                        title = f"{chat.first_name}"
                        if hasattr(chat, 'last_name') and chat.last_name:
                            title += f" {chat.last_name}"
                    else:
                        title = "Unknown"

                    return {
                        'type': chat_type,
                        'id': chat.id,
                        'title': title,
                        'username': getattr(chat, 'username', None),
                        'members_count': getattr(chat, 'members_count', None),
                        'description': getattr(chat, 'description', None)
                    }

                except Exception as e:
                    logger.warning(f"Could not resolve {identifier}: {e}")
                    return None

        except Exception as e:
            logger.error(f"Error in _resolve_telegram_link: {e}")
            return None

    async def _handle_private_invite_link(self, invite_link: str) -> dict:
        """
        Handle private invite links by attempting to join and get info

        Args:
            invite_link: Private invite link

        Returns:
            Dict with group information or None if failed
        """
        try:
            # Try to join the chat using the invite link
            chat = await self.app.join_chat(invite_link)

            # Get chat information after joining
            chat_info = await self.app.get_chat(chat.id)

            # Determine the type
            chat_type = "Private Group"
            if hasattr(chat_info, 'type'):
                if chat_info.type.name == "GROUP":
                    chat_type = "Private Group"
                elif chat_info.type.name == "SUPERGROUP":
                    chat_type = "Private Supergroup"
                elif chat_info.type.name == "CHANNEL":
                    chat_type = "Private Channel"

            return {
                'type': chat_type,
                'id': chat_info.id,
                'title': chat_info.title or "Private Group",
                'username': getattr(chat_info, 'username', None),
                'members_count': getattr(chat_info, 'members_count', None),
                'description': getattr(chat_info, 'description', None),
                'invite_link': invite_link,
                'joined_via_link': True
            }

        except Exception as e:
            # If joining fails, try to extract limited info from the link
            logger.warning(f"Could not join private group via {invite_link}: {e}")

            # Return limited information
            return {
                'type': 'Private Group (Not Accessible)',
                'id': 'Unknown',
                'title': 'Private Group',
                'username': None,
                'members_count': None,
                'description': f"Private invite link: {invite_link}",
                'invite_link': invite_link,
                'joined_via_link': False,
                'error': str(e)
            }

    def _parse_telegram_identifier(self, link_or_username: str) -> str:
        """
        Parse various Telegram link formats and usernames

        Args:
            link_or_username: Input string

        Returns:
            Cleaned identifier for Telegram API
        """
        import re

        # Remove whitespace
        identifier = link_or_username.strip()

        # Handle different formats
        if identifier.startswith('https://t.me/'):
            # Extract from t.me links
            if '/joinchat/' in identifier:
                # Old style private group invite link
                return identifier
            elif identifier.startswith('https://t.me/+'):
                # New style private group invite link (starts with +)
                return identifier
            else:
                # Public username link
                username = identifier.replace('https://t.me/', '')
                # Remove any additional parameters
                username = username.split('?')[0].split('/')[0]
                return username

        elif identifier.startswith('http://t.me/'):
            # Handle http variant
            username = identifier.replace('http://t.me/', '')
            username = username.split('?')[0].split('/')[0]
            return username

        elif identifier.startswith('@'):
            # Username with @
            return identifier[1:]  # Remove @

        elif identifier.startswith('+'):
            # Private invite hash (new format)
            return f"https://t.me/{identifier}"

        elif identifier.startswith('-100') or identifier.startswith('-'):
            # Group/channel ID
            try:
                int(identifier)  # Validate it's a number
                return identifier
            except ValueError:
                return None

        elif identifier.isdigit():
            # User ID
            return identifier

        else:
            # Assume it's a username without @
            # Validate username format
            if re.match(r'^[a-zA-Z0-9_]{5,32}$', identifier):
                return identifier
            else:
                return None

    async def start(self):
        """Start the bot"""
        try:
            await self.app.start()
            me = await self.app.get_me()
            logger.info(f"Bot started successfully: @{me.username}")
            print(f"🤖 Bot started successfully: @{me.username}")
            print("📱 Send /start to the bot to begin!")
            
            # Keep the bot running
            await asyncio.Event().wait()
            
        except (ApiIdInvalid, ApiIdPublishedFlood) as e:
            logger.error(f"Invalid API credentials: {e}")
            print("❌ Invalid API_ID or API_HASH. Please check your credentials in .env file")
            
        except AccessTokenInvalid as e:
            logger.error(f"Invalid bot token: {e}")
            print("❌ Invalid BOT_TOKEN. Please check your bot token in config.py")
            
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            print(f"❌ Error starting bot: {e}")
    
    async def stop(self):
        """Stop the bot"""
        await self.app.stop()
        logger.info("Bot stopped")


async def main():
    """Main function"""
    bot = TelegramUsersGrabberBot()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
    finally:
        await bot.stop()


if __name__ == "__main__":
    asyncio.run(main())

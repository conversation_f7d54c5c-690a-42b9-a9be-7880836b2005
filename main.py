import asyncio
import logging
import os
from pyrogram import Client, filters
from pyrogram.types import Message
from pyrogram.errors import ApiIdInvalid, ApiIdPublishedFlood, AccessTokenInvalid

from config import BOT_TOKEN, API_ID, API_HASH, SESSION_NAME, COMMANDS
from user_extractor import UserExtractor
from csv_exporter import CSVExporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize components
csv_exporter = CSVExporter()


class TelegramUsersGrabberBot:
    """Main bot class for Telegram Users Grabber"""
    
    def __init__(self):
        if not API_ID or not API_HASH:
            raise ValueError(
                "API_ID and API_HASH are required. Please create a .env file with your credentials.\n"
                "Get them from https://my.telegram.org/apps"
            )
        
        self.app = Client(
            SESSION_NAME,
            api_id=API_ID,
            api_hash=API_HASH,
            bot_token=BOT_TOKEN
        )
        
        self.user_extractor = UserExtractor(self.app)
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup message handlers"""
        
        @self.app.on_message(filters.command("start"))
        async def start_command(client, message: Message):
            welcome_text = """
🤖 **Telegram Users Grabber Bot**

This bot can extract user lists from Telegram groups and export them to CSV files.

**Available Commands:**
/start - Show this welcome message
/help - Show detailed help
/extract <group_id_or_username> - Extract users from a group (bot method)
/extract_advanced <group_id_or_username> - Advanced extraction with multiple methods

**Usage Examples:**
• `/extract @mygroup` - Extract from public group (standard method)
• `/extract -1001234567890` - Extract from group using ID
• `/extract_advanced @mygroup` - Use advanced extraction methods

**Requirements:**
1. The bot must be added to the target group
2. The bot needs admin rights to access member lists
3. For private groups, you need the exact group ID

**Important Notes:**
• Due to Telegram's privacy restrictions, the bot can only extract users who are visible
• For large groups/channels, only admins, bots, and recently active users may be accessible
• The bot will also try to extract users from recent message history
• Small groups (< 200 members) usually allow full member access

Type /help for more detailed instructions.
            """
            await message.reply_text(welcome_text)
        
        @self.app.on_message(filters.command("help"))
        async def help_command(client, message: Message):
            help_text = """
📖 **Detailed Help - Telegram Users Grabber Bot**

**How to use this bot:**

1️⃣ **Add bot to your group:**
   - Add this bot to the target group
   - Give it admin rights (needed to access member list)

2️⃣ **Get group information:**
   - For public groups: Use @username (e.g., @mygroup)
   - For private groups: Use group ID (e.g., -1001234567890)
   
3️⃣ **Extract users:**
   - Use `/extract @groupname` or `/extract -1001234567890`
   - Wait for the process to complete
   - Download the CSV file when ready

**CSV File Contents:**
The exported CSV will include:
• User ID, Username, First/Last Name
• Phone number (if visible)
• Bot status, Verification status
• Premium status, Admin status
• Last online date, Join date
• And more user information

**Important Limitations:**
• **Privacy Restrictions**: Due to Telegram's privacy policy, bots can only see:
  - Administrators and their permissions
  - Bots in the group
  - Users who have recently sent messages (if bot has message access)
  - In small groups (< 200 members): usually all members
• **Large Groups/Channels**: Only admins, bots, and recently active users are visible
• **Rate Limits**: Very large groups may trigger rate limits
• **Admin Rights**: Bot needs admin rights to access member lists
• **Message History**: Bot will try to extract additional users from recent messages

**Troubleshooting:**
• "Invalid group" - Make sure bot is added to the group
• "Admin required" - Give bot admin rights
• "Rate limited" - Wait and try again later

Need more help? Contact the bot developer.
            """
            await message.reply_text(help_text)
        
        @self.app.on_message(filters.command("extract"))
        async def extract_command(client, message: Message):
            # Check if group identifier is provided
            if len(message.command) < 2:
                await message.reply_text(
                    "❌ Please provide a group ID or username.\n\n"
                    "**Usage:**\n"
                    "• `/extract @groupname`\n"
                    "• `/extract -1001234567890`"
                )
                return
            
            group_identifier = message.command[1]
            user_id = message.from_user.id
            
            # Send initial status message
            status_message = await message.reply_text("🔄 Starting extraction process...")
            
            try:
                # Progress callback function
                async def progress_callback(status_text):
                    try:
                        await status_message.edit_text(status_text)
                    except Exception:
                        pass  # Ignore edit errors
                
                # Extract users
                users_data, group_name = await self.user_extractor.extract_users_from_group(
                    group_identifier, progress_callback
                )
                
                if not users_data:
                    await status_message.edit_text("❌ No users found in the group.")
                    return
                
                # Update status
                await progress_callback("📊 Generating CSV file...")
                
                # Export to CSV
                csv_filepath = csv_exporter.export_users_to_csv(users_data, group_name)
                
                # Get statistics
                stats = csv_exporter.get_user_stats(users_data)
                
                # Prepare statistics message
                stats_text = f"""
✅ **Extraction Complete!**

📊 **Statistics:**
• Total Users: {stats.get('total_users', 0)}
• Bots: {stats.get('bots', 0)}
• Verified Users: {stats.get('verified_users', 0)}
• Premium Users: {stats.get('premium_users', 0)}
• Users with Username: {stats.get('users_with_username', 0)}
• Users with Phone: {stats.get('users_with_phone', 0)}

📁 **Group:** {group_name}
📄 **File:** {os.path.basename(csv_filepath)}
                """
                
                # Send CSV file
                await message.reply_document(
                    document=csv_filepath,
                    caption=stats_text
                )
                
                # Clean up status message
                await status_message.delete()
                
                # Clean up CSV file after sending
                try:
                    os.remove(csv_filepath)
                except Exception:
                    pass
                
            except Exception as e:
                error_message = f"❌ **Error:** {str(e)}"
                await status_message.edit_text(error_message)
                logger.error(f"Extraction error for user {user_id}: {e}")
    
    async def start(self):
        """Start the bot"""
        try:
            await self.app.start()
            me = await self.app.get_me()
            logger.info(f"Bot started successfully: @{me.username}")
            print(f"🤖 Bot started successfully: @{me.username}")
            print("📱 Send /start to the bot to begin!")
            
            # Keep the bot running
            await asyncio.Event().wait()
            
        except (ApiIdInvalid, ApiIdPublishedFlood) as e:
            logger.error(f"Invalid API credentials: {e}")
            print("❌ Invalid API_ID or API_HASH. Please check your credentials in .env file")
            
        except AccessTokenInvalid as e:
            logger.error(f"Invalid bot token: {e}")
            print("❌ Invalid BOT_TOKEN. Please check your bot token in config.py")
            
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            print(f"❌ Error starting bot: {e}")
    
    async def stop(self):
        """Stop the bot"""
        await self.app.stop()
        logger.info("Bot stopped")


async def main():
    """Main function"""
    bot = TelegramUsersGrabberBot()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
    finally:
        await bot.stop()


if __name__ == "__main__":
    asyncio.run(main())

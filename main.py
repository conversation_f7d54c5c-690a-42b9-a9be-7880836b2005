import asyncio
import logging
import os
from pyrogram import Client, filters
from pyrogram.types import Message
from pyrogram.errors import ApiIdInvalid, ApiIdPublishedFlood, AccessTokenInvalid

from config import BOT_TOKEN, API_ID, API_HASH, SESSION_NAME, COMMANDS
from user_extractor import UserExtractor
from message_user_extractor import MessageUserExtractor
from csv_exporter import CSVExporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize components
csv_exporter = CSVExporter()


class TelegramUsersGrabberBot:
    """Main bot class for Telegram Users Grabber"""
    
    def __init__(self):
        if not API_ID or not API_HASH:
            raise ValueError(
                "API_ID and API_HASH are required. Please create a .env file with your credentials.\n"
                "Get them from https://my.telegram.org/apps"
            )
        
        self.app = Client(
            SESSION_NAME,
            api_id=API_ID,
            api_hash=API_HASH,
            bot_token=BOT_TOKEN
        )
        
        self.user_extractor = UserExtractor(self.app)
        self.message_extractor = MessageUserExtractor(self.app)
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup message handlers"""
        
        @self.app.on_message(filters.command("start"))
        async def start_command(client, message: Message):
            welcome_text = """
🤖 **Telegram Users Grabber Bot**

This bot can extract user lists from Telegram groups and export them to CSV files.

**Available Commands:**
/start - Show this welcome message
/help - Show detailed help
/extract <group_id_or_username> - Extract available participants (bot method)
/extract_messages <group_id_or_username> - Extract users from messages (user method)

**Usage Examples:**
• `/extract @mygroup` - Extract participants using bot method
• `/extract_messages @mygroup` - Extract users from messages (more comprehensive)
• `/extract -*************` - Extract from group using ID

**Requirements:**
1. The bot must be added to the target group
2. The bot needs admin rights to access member lists
3. For private groups, you need the exact group ID

**Important Limitations (Telegram's Bot Restrictions):**
• **Large Groups/Channels**: Bots can only see admins, bots, and some recent members
• **Private Groups**: Very limited access for bots
• **Small Groups** (< 200 members): Usually better access
• **Message History**: Bots cannot access message history in most groups
• **Best Results**: Use with small, public groups where bot has admin rights

Type /help for more detailed instructions.
            """
            await message.reply_text(welcome_text)
        
        @self.app.on_message(filters.command("help"))
        async def help_command(client, message: Message):
            help_text = """
📖 **Detailed Help - Telegram Users Grabber Bot**

**How to use this bot:**

1️⃣ **Add bot to your group:**
   - Add this bot to the target group
   - Give it admin rights (needed to access member list)

2️⃣ **Choose extraction method:**
   - **Bot Method** (`/extract`): Fast, limited to visible participants
   - **Message Method** (`/extract_messages`): Comprehensive, extracts from messages

3️⃣ **Get group information:**
   - For public groups: Use @username (e.g., @mygroup)
   - For private groups: Use group ID (e.g., -*************)

4️⃣ **Extract users:**
   - **Standard:** `/extract @groupname` (bot method)
   - **Advanced:** `/extract_messages @groupname` (user method, requires phone verification)
   - Wait for the process to complete
   - Download the CSV file when ready

**CSV File Contents:**
The exported CSV will include:
• User ID, Username, First/Last Name
• Phone number (if visible)
• Bot status, Verification status
• Premium status, Admin status
• Last online date, Join date
• And more user information

**Important Limitations (Telegram Bot API Restrictions):**
• **What Bots Can See**:
  - ✅ Administrators and their permissions
  - ✅ Bots in the group
  - ✅ Some recently active members (limited)
  - ✅ In small groups (< 200 members): usually more members
• **What Bots Cannot Do**:
  - ❌ Access full member lists in large groups/channels
  - ❌ Read message history in most groups
  - ❌ See private user information
  - ❌ Access members in private channels without invitation
• **Best Results**: Small, public groups where bot has admin rights
• **Alternative**: For full access, you'd need to use a user account (not a bot)

**Troubleshooting:**
• "Invalid group" - Make sure bot is added to the group
• "Admin required" - Give bot admin rights
• "Rate limited" - Wait and try again later

Need more help? Contact the bot developer.
            """
            await message.reply_text(help_text)
        
        @self.app.on_message(filters.command("extract"))
        async def extract_command(client, message: Message):
            # Check if group identifier is provided
            if len(message.command) < 2:
                await message.reply_text(
                    "❌ Please provide a group ID or username.\n\n"
                    "**Usage:**\n"
                    "• `/extract @groupname`\n"
                    "• `/extract -*************`"
                )
                return
            
            group_identifier = message.command[1]
            user_id = message.from_user.id
            
            # Send initial status message
            status_message = await message.reply_text("🔄 Starting extraction process...")
            
            try:
                # Progress callback function
                async def progress_callback(status_text):
                    try:
                        await status_message.edit_text(status_text)
                    except Exception:
                        pass  # Ignore edit errors
                
                # Extract users
                users_data, group_name = await self.user_extractor.extract_users_from_group(
                    group_identifier, progress_callback
                )
                
                if not users_data:
                    await status_message.edit_text("❌ No users found in the group.")
                    return
                
                # Update status
                await progress_callback("📊 Generating CSV file...")
                
                # Export to CSV
                csv_filepath = csv_exporter.export_users_to_csv(users_data, group_name)
                
                # Get statistics
                stats = csv_exporter.get_user_stats(users_data)
                
                # Prepare statistics message
                stats_text = f"""
✅ **Extraction Complete!**

📊 **Statistics:**
• Total Users: {stats.get('total_users', 0)}
• Bots: {stats.get('bots', 0)}
• Verified Users: {stats.get('verified_users', 0)}
• Premium Users: {stats.get('premium_users', 0)}
• Users with Username: {stats.get('users_with_username', 0)}
• Users with Phone: {stats.get('users_with_phone', 0)}

📁 **Group:** {group_name}
📄 **File:** {os.path.basename(csv_filepath)}
                """
                
                # Send CSV file
                await message.reply_document(
                    document=csv_filepath,
                    caption=stats_text
                )
                
                # Clean up status message
                await status_message.delete()
                
                # Clean up CSV file after sending
                try:
                    os.remove(csv_filepath)
                except Exception:
                    pass

            except Exception as e:
                error_message = f"❌ **Error:** {str(e)}"
                await status_message.edit_text(error_message)
                logger.error(f"Extraction error for user {user_id}: {e}")

        @self.app.on_message(filters.command("extract_messages"))
        async def extract_messages_command(client, message: Message):
            # Check if group identifier is provided
            if len(message.command) < 2:
                await message.reply_text(
                    "❌ Please provide a group ID or username.\n\n"
                    "**Usage:**\n"
                    "• `/extract_messages @groupname`\n"
                    "• `/extract_messages -*************`\n\n"
                    "**Note:** This method acts like a user and extracts users from messages. "
                    "It requires phone verification on first use."
                )
                return

            group_identifier = message.command[1]
            user_id = message.from_user.id

            # Send initial status message
            status_message = await message.reply_text("🔄 Initializing user session for message extraction...")

            try:
                # Initialize user session
                await status_message.edit_text("📱 Setting up user session (may require phone verification)...")

                success = await self.message_extractor.create_user_session(f"user_session_{user_id}")
                if not success:
                    await status_message.edit_text(
                        "❌ **Failed to create user session**\n\n"
                        "This method requires a user account session. "
                        "Please make sure you have valid API credentials and can verify your phone number."
                    )
                    return

                # Progress callback function
                async def progress_callback(status_text):
                    try:
                        await status_message.edit_text(status_text)
                    except Exception:
                        pass  # Ignore edit errors

                # Extract users from messages
                users_data, group_name = await self.message_extractor.extract_users_from_messages(
                    group_identifier, progress_callback
                )

                if not users_data:
                    await status_message.edit_text("❌ No users found in the group messages.")
                    return

                # Update status
                await progress_callback("📊 Generating CSV file...")

                # Export to CSV
                csv_filepath = csv_exporter.export_users_to_csv(users_data, group_name)

                # Get statistics
                stats = csv_exporter.get_user_stats(users_data)

                # Prepare statistics message
                stats_text = f"""
✅ **Message Extraction Complete!**

📊 **Statistics:**
• Total Users: {stats.get('total_users', 0)}
• Bots: {stats.get('bots', 0)}
• Verified Users: {stats.get('verified_users', 0)}
• Premium Users: {stats.get('premium_users', 0)}
• Users with Username: {stats.get('users_with_username', 0)}

📁 **Group:** {group_name}
📄 **File:** {os.path.basename(csv_filepath)}
🔍 **Method:** Message-based extraction (user session)
                """

                # Send CSV file
                await message.reply_document(
                    document=csv_filepath,
                    caption=stats_text
                )

                # Clean up status message
                await status_message.delete()

                # Clean up CSV file after sending
                try:
                    os.remove(csv_filepath)
                except Exception:
                    pass

                # Stop user client
                await self.message_extractor.stop_user_client()

            except Exception as e:
                error_message = f"❌ **Error:** {str(e)}"
                await status_message.edit_text(error_message)
                logger.error(f"Message extraction error for user {user_id}: {e}")

                # Try to stop user client on error
                try:
                    await self.message_extractor.stop_user_client()
                except Exception:
                    pass
    
    async def start(self):
        """Start the bot"""
        try:
            await self.app.start()
            me = await self.app.get_me()
            logger.info(f"Bot started successfully: @{me.username}")
            print(f"🤖 Bot started successfully: @{me.username}")
            print("📱 Send /start to the bot to begin!")
            
            # Keep the bot running
            await asyncio.Event().wait()
            
        except (ApiIdInvalid, ApiIdPublishedFlood) as e:
            logger.error(f"Invalid API credentials: {e}")
            print("❌ Invalid API_ID or API_HASH. Please check your credentials in .env file")
            
        except AccessTokenInvalid as e:
            logger.error(f"Invalid bot token: {e}")
            print("❌ Invalid BOT_TOKEN. Please check your bot token in config.py")
            
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            print(f"❌ Error starting bot: {e}")
    
    async def stop(self):
        """Stop the bot"""
        await self.app.stop()
        logger.info("Bot stopped")


async def main():
    """Main function"""
    bot = TelegramUsersGrabberBot()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
    finally:
        await bot.stop()


if __name__ == "__main__":
    asyncio.run(main())
